import { Hono, Context, Next } from "hono";
import { SupabaseClient } from "@supabase/supabase-js";
import { supabaseMiddleware } from "../middleware/supabaseMiddleware";
import express, { Request, Response } from "express";
import {
  GenerateQuizParams,
  GenerateQuizResponse,
  QuizQuestion as SharedQuizQuestion,
  QuizQuestionOption,
  AIProviderConfig,
  QuizQuestionBatchInsertPayload,
  Quiz,
} from "../../shared/types/quiz";
import {
  UserCompletionInsert,
  QuizCompletionData,
  CompletionStats,
  CompletionFilters,
} from "../../shared/types/completion";
import { v4 as uuidv4 } from "uuid";

// Extend RequestInit to include Node.js specific duplex option
interface NodeRequestInit extends RequestInit {
  duplex?: "half" | "full";
}

// Fix for Node.js 18+ fetch duplex issue
if (typeof globalThis.fetch === "undefined") {
  globalThis.fetch = require("node-fetch");
}

type AppVariables = {
  supabase: SupabaseClient;
  user: { id: string };
};

console.log("quizRoutes.ts: Module loaded"); // Added for debugging

const quizRoutes = new Hono<{ Variables: AppVariables }>();

// Apply the Supabase middleware to all routes
quizRoutes.use("*", supabaseMiddleware);

// Application-level error handler for quizRoutes
// This ensures that any unhandled error within quizRoutes still returns a JSON response.
quizRoutes.onError((err, c) => {
  console.error("Error in quizRoutes:", err);
  // Optionally include stack in development
  // const stack = process.env.NODE_ENV === 'development' ? err.stack : undefined;
  return c.json(
    {
      error: "An unexpected error occurred in quiz routes.",
      message: err.message,
      // stack
    },
    500
  );
});

// Middleware to ensure user is authenticated
const authMiddleware = async (
  c: Context<{ Variables: AppVariables }>,
  next: Next
) => {
  console.log(`quizRoutes: Auth middleware triggered for path: ${c.req.path}`); // Log 1
  const authHeader = c.req.header("Authorization");

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    console.error(
      "quizRoutes: Auth Error - Authorization header missing or malformed."
    ); // Log E1
    return c.json({ error: "Unauthorized: Missing or malformed token" }, 401);
  }

  const token = authHeader.split(" ")[1];
  if (!token) {
    console.error("quizRoutes: Auth Error - Token missing after Bearer split."); // Log E2
    return c.json({ error: "Unauthorized: Missing token" }, 401);
  }

  const supabase = c.get("supabase");
  if (!supabase) {
    console.error(
      "quizRoutes: Auth Error - Supabase client not found in request context."
    ); // Log E3
    return c.json(
      { error: "Server configuration error: Supabase client missing" },
      500
    );
  }

  console.log(
    "quizRoutes: Auth - Token is present. Attempting supabase.auth.getUser(token)."
  ); // Log 2
  try {
    const { data, error: getUserError } = await supabase.auth.getUser(token);

    if (getUserError) {
      console.error(
        `quizRoutes: Auth Error - supabase.auth.getUser returned an error: ${getUserError.message}`,
        getUserError
      ); // Log E4
      if (
        getUserError.message.toLowerCase().includes("invalid token") ||
        getUserError.message.includes("jwt")
      ) {
        return c.json(
          {
            error: "Unauthorized: Invalid token",
            details: getUserError.message,
          },
          401
        );
      }
      return c.json(
        {
          error: "Server error validating token",
          details: getUserError.message,
        },
        500
      );
    }

    const user = data?.user; // Check data.user specifically

    if (!user) {
      console.error(
        "quizRoutes: Auth Error - No user object found in supabase.auth.getUser response (data.user is null/undefined)."
      ); // Log E5
      return c.json({ error: "Unauthorized: No user found for token" }, 401);
    }

    console.log(
      `quizRoutes: Auth Success - User ${user.id} authenticated. Calling next().`
    ); // Log 4
    c.set("user", user);
    await next();
  } catch (err: any) {
    console.error(
      "quizRoutes: Auth Fatal Error - Unexpected error in auth middleware try-catch block:",
      err.message,
      err.stack
    ); // Log E6
    // In Hono, if an error occurs after next() has been called and potentially a response sent by a later handler,
    // this return might not execute or might cause issues if a response was already committed.
    // However, for errors *before* or *during* c.json(), this is fine.
    // If next() itself throws and doesn't handle its own response, this catch block will execute.
    return c.json(
      { error: "Internal server error during authentication processing" },
      500
    );
  }
};

quizRoutes.use("*", authMiddleware);

interface AIPromptPayload {
  model: string;
  messages: {
    role: "user" | "assistant" | "system";
    content: string;
  }[];
  temperature?: number;
  max_tokens?: number;
  // Add other parameters as supported by OpenRouter and the model
}

interface AIResponseFormat {
  questions: AIGeneratedQuestion[];
}
interface AIGeneratedQuestion {
  questionText: string;
  type:
    | "multiple_choice"
    | "select_all_that_apply"
    | "true_false"
    | "short_answer";
  options?: Array<{ text: string; is_correct?: boolean }>;
  correctAnswer?: string | boolean | string[];
  explanation?: string;
}

// Helper function to call AI (OpenRouter)
async function generateQuestionsFromAI(
  textContent: string,
  numberOfQuestions: number,
  aiConfig: AIProviderConfig,
  quizTitle: string,
  customPromptText?: string,
  requestedQuestionTypes?: string[]
): Promise<AIResponseFormat | null> {
  const { apiKey, baseUrl, model } = aiConfig;

  // Validate AI configuration
  if (!apiKey || !apiKey.trim()) {
    console.error("AI API key is missing or empty");
    throw new Error(
      "AI API key is required but not provided. Please configure your AI provider settings."
    );
  }

  if (!baseUrl || !baseUrl.trim()) {
    console.error("AI base URL is missing or empty");
    throw new Error(
      "AI base URL is required but not provided. Please configure your AI provider settings."
    );
  }

  console.log(
    `Generating ${numberOfQuestions} questions using model: ${
      model || "google/gemini-2.5-pro-preview"
    }`
  );

  const questionTypesString =
    requestedQuestionTypes && requestedQuestionTypes.length > 0
      ? requestedQuestionTypes.join(", ")
      : "multiple_choice, true_false, short_answer, select_all_that_apply"; // Default if not specified

  console.log(`AI Generation: Requested question types: [${questionTypesString}]`);

  const basePrompt = `Based on the following text, generate a quiz titled "${quizTitle}" with exactly ${numberOfQuestions} questions.

IMPORTANT: You MUST generate questions ONLY of the following types: ${questionTypesString}
Do NOT generate questions of any other types. Every single question must be one of these exact types: ${questionTypesString}.

For each question, provide:
1. "questionText": The text of the question (string).
2. "type": The type of question (string, must be one of "multiple_choice", "select_all_that_apply", "true_false", "short_answer").
3. "options": (ONLY for "multiple_choice" and "select_all_that_apply") An array of 3 to 5 option objects. Each option object must have a "text" (string) key. For "multiple_choice", one option should have "is_correct": true. For "select_all_that_apply", one or more options should have "is_correct": true. Other options should have "is_correct": false or omit it.
4. "correctAnswer":
   - For "true_false": The string "true" or "false".
   - For "short_answer": The concise correct answer string.
   - For "multiple_choice": The text of the single correct option. (This is redundant if "is_correct" is in options, but good for AI to provide).
   - For "select_all_that_apply": An array of strings, where each string is the text of a correct option. (Also redundant if "is_correct" is in options).
5. "explanation": (Optional) A brief explanation for why the answer is correct (string).

Return the output as a valid JSON object with a single key "questions". The value of "questions" should be an array of question objects adhering to the structure described above.`;

  const customInstructions = customPromptText
    ? `\n\nAdditional instructions for question generation: ${customPromptText}`
    : "";
  const prompt = `${basePrompt}${customInstructions}\n\nText:\n"""\n${textContent.substring(
    0,
    15000
  )}\n"""\n\nOutput only the JSON object.`;

  console.log("AI Prompt being sent:", prompt.substring(0, 500) + "..."); // Log part of the prompt

  const payload: AIPromptPayload = {
    model: model || "google/gemini-2.5-pro-preview", // Default to gemini-2.5-pro-preview as per OpenRouter
    messages: [{ role: "user", content: prompt }],
    // temperature: 0.7, // Adjust as needed
    // max_tokens: 2000, // Adjust as needed, ensure it's enough for the number of questions
  };

  try {
    console.log(`Making AI API call to: ${baseUrl}/chat/completions`);
    const response = await fetch(`${baseUrl}/chat/completions`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
      duplex: "half", // Required for Node.js 18+ when sending a body
    } as NodeRequestInit);

    if (!response.ok) {
      console.error(`AI API error: ${response.status} ${response.statusText}`);
      const errorText = await response.text();
      console.error("AI API error response:", errorText);

      // Provide more specific error messages based on status code
      if (response.status === 401) {
        throw new Error(
          "Unauthorized: Invalid API key. Please check your AI provider API key."
        );
      } else if (response.status === 403) {
        throw new Error(
          "Forbidden: API key does not have permission for this operation."
        );
      } else if (response.status === 429) {
        throw new Error(
          "Rate limited: Too many requests. Please try again later."
        );
      } else {
        throw new Error(`AI provider error (${response.status}): ${errorText}`);
      }
    }

    const responseData = await response.json();

    if (
      responseData &&
      responseData.choices &&
      responseData.choices[0] &&
      responseData.choices[0].message
    ) {
      const content = responseData.choices[0].message.content;
      console.log("AI response received, parsing JSON...");

      // The AI might return the JSON as a string, sometimes wrapped in markdown ```json ... ```
      const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/);
      const jsonString = jsonMatch ? jsonMatch[1] : content;
      try {
        const result = JSON.parse(jsonString) as AIResponseFormat;
        console.log(
          `Successfully parsed ${
            result.questions?.length || 0
          } questions from AI response`
        );

        // Filter questions to only include the requested types
        if (requestedQuestionTypes && requestedQuestionTypes.length > 0 && result.questions) {
          const originalCount = result.questions.length;
          result.questions = result.questions.filter(q =>
            requestedQuestionTypes.includes(q.type)
          );
          const filteredCount = result.questions.length;

          if (originalCount !== filteredCount) {
            console.log(
              `AI Generation: Filtered out ${originalCount - filteredCount} questions that didn't match requested types. ` +
              `Kept ${filteredCount} questions of types: [${requestedQuestionTypes.join(", ")}]`
            );
          }
        }

        return result;
      } catch (parseError) {
        console.error(
          "Error parsing AI response JSON:",
          parseError,
          "Raw content:",
          content
        );
        // Attempt to find JSON within a potentially messy string
        const relaxedJsonMatch = content.match(/{[\s\S]*}/);
        if (relaxedJsonMatch && relaxedJsonMatch[0]) {
          try {
            const result = JSON.parse(relaxedJsonMatch[0]) as AIResponseFormat;
            console.log(
              `Successfully parsed ${
                result.questions?.length || 0
              } questions from relaxed JSON match`
            );

            // Filter questions to only include the requested types
            if (requestedQuestionTypes && requestedQuestionTypes.length > 0 && result.questions) {
              const originalCount = result.questions.length;
              result.questions = result.questions.filter(q =>
                requestedQuestionTypes.includes(q.type)
              );
              const filteredCount = result.questions.length;

              if (originalCount !== filteredCount) {
                console.log(
                  `AI Generation (relaxed): Filtered out ${originalCount - filteredCount} questions that didn't match requested types. ` +
                  `Kept ${filteredCount} questions of types: [${requestedQuestionTypes.join(", ")}]`
                );
              }
            }

            return result;
          } catch (relaxedParseError) {
            console.error(
              "Error parsing relaxed AI response JSON:",
              relaxedParseError,
              "Raw content:",
              content
            );
            throw new Error(
              "Failed to parse AI response as valid JSON. The AI response format was unexpected."
            );
          }
        }
        throw new Error(
          "Failed to parse AI response as valid JSON. The AI response format was unexpected."
        );
      }
    } else {
      console.error("Unexpected AI response structure:", responseData);
      throw new Error(
        "AI response did not contain expected message structure."
      );
    }
  } catch (error) {
    console.error("Error calling AI API:", error);
    // Re-throw the error instead of returning null so the calling function can handle it properly
    throw error;
  }
}

// POST /api/quizzes/generate
quizRoutes.post(
  "/generate",
  async (c: Context<{ Variables: AppVariables }>) => {
    const supabase = c.get("supabase");
    const user = c.get("user");
    try {
      console.log(
        `[quizRoutes] POST /generate handler reached. Path: ${c.req.path}, Method: ${c.req.method}`
      );
      const requestBody = await c.req.json();
      console.log(
        "Quiz generation request body:",
        JSON.stringify(requestBody, null, 2)
      );

      // Handle both payload formats:
      // Format 1 (UploadSection): { textContent, documentId, quizTitle, numberOfQuestions, aiConfig }
      // Format 2 (CreateQuizForm): { documentIds, quizName, quizDescription, generationOptions }

      let textContent: string;
      let documentId: string | null;
      let quizTitle: string;
      let numberOfQuestions: number;
      let aiConfig: AIProviderConfig;
      let generationOptions: GenerateQuizParams["generationOptions"];
      let customPrompt: string | undefined;

      if (requestBody.textContent && requestBody.quizTitle) {
        // Format 1 (UploadSection) - Legacy format
        textContent = requestBody.textContent;
        documentId = requestBody.documentId || null;
        quizTitle = requestBody.quizTitle;
        numberOfQuestions = requestBody.numberOfQuestions;
        aiConfig = requestBody.aiConfig;
        customPrompt = requestBody.customPrompt;
        // Infer generationOptions if not explicitly provided in this format, or use what's passed
        generationOptions = requestBody.generationOptions || {
          numberOfQuestions: numberOfQuestions,
          questionTypes: requestBody.questionTypes || ["multiple_choice"], // Use provided questionTypes or default
        };
      } else if (requestBody.textContent && requestBody.quizName && requestBody.generationOptions) {
        // Format 1.5 (UploadSection) - New format with textContent and generationOptions
        textContent = requestBody.textContent;
        documentId = requestBody.documentId || null;
        quizTitle = requestBody.quizName; // This is correct
        // Ensure generationOptions is correctly picked up from requestBody for Format 1.5
        generationOptions = requestBody.generationOptions; // This should contain numberOfQuestions and questionTypes
        numberOfQuestions = requestBody.generationOptions.numberOfQuestions;
        customPrompt = requestBody.customPrompt;
        aiConfig = requestBody.aiConfig;
      } else if (requestBody.quizName && requestBody.generationOptions) {
        // Format 2 (CreateQuizForm) - need to get document content
        const documentIds = requestBody.documentIds || [];
        if (documentIds.length === 0) {
          return c.json(
            { error: "No document IDs provided for AI generation" },
            400
          );
        }

        // Handle multiple documents
        let combinedTextContent = "";
        for (const docId of documentIds) {
          const { data: document, error: docError } = await supabase
            .from("study_documents") // Fetch file_name for context
            .select("extracted_text_path, user_id, file_name")
            .eq("id", docId)
            .single();

          if (docError || !document) {
            return c.json(
              { error: `Document not found for ID: ${docId}` },
              404
            );
          }
          if (document.user_id !== user.id) {
            return c.json(
              {
                error: `Forbidden: Document ${docId} does not belong to this user`,
              },
              403
            );
          }
          if (!document.extracted_text_path) {
            return c.json(
              {
                error: `Document ${docId} (${document.file_name}) has no extracted text path.`,
              },
              400
            );
          }

          const { data: fileData, error: downloadError } =
            await supabase.storage
              .from("study_materials") // Assuming 'study_materials' is the correct bucket
              .download(document.extracted_text_path);

          if (downloadError || !fileData) {
            console.error(
              `Error downloading content for document ${docId}:`,
              downloadError
            );
            return c.json(
              {
                error: `Failed to download content for document ${docId} (${document.file_name})`,
                details: downloadError?.message,
              },
              500
            );
          }

          const docTextContent = await fileData.text();
          combinedTextContent += `Content from ${document.file_name}:\n${docTextContent}\n\n`;
        }
        textContent = combinedTextContent.trim();
        documentId = documentIds.join(","); // Store comma-separated IDs or handle differently

        quizTitle = requestBody.quizName;
        generationOptions = requestBody.generationOptions; // FIX: Assign the generationOptions object
        numberOfQuestions = requestBody.generationOptions.numberOfQuestions;
        customPrompt = requestBody.generationOptions.customPrompt;

        // Get AI config from stored user credentials
        const { getEphemeralUserCredentials } = await import('../middleware/apiKeyStorage');
        const credentialsResult = await getEphemeralUserCredentials(userId, 'OpenRouter');

        if (!credentialsResult.success || !credentialsResult.credentials) {
          return c.json(
            {
              error: "AI Provider not configured. Please configure your AI settings first.",
              details: "No stored credentials found for OpenRouter provider"
            },
            400
          );
        }

        aiConfig = {
          apiKey: credentialsResult.credentials.apiKey,
          baseUrl: credentialsResult.credentials.baseUrl,
          model: credentialsResult.credentials.generationModel,
        };
      } else {
        return c.json(
          {
            error:
              "Invalid request format. Expected either { textContent, quizTitle, numberOfQuestions, aiConfig } or { textContent, quizName, generationOptions, aiConfig } or { quizName, documentIds, generationOptions }",
          },
          400
        );
      }

      if (!textContent || !quizTitle || !numberOfQuestions) {
        return c.json( // This check might be redundant if generationOptions is always present for Format 2
          {
            error: "Missing required fields after processing request",
          },
          400
        );
      }

      // Debug logging for generationOptions
      console.log("Backend: Received generationOptions:", JSON.stringify(generationOptions, null, 2));
      console.log("Backend: Question types received:", generationOptions?.questionTypes);
      console.log("Backend: Question types length:", generationOptions?.questionTypes?.length);

      // Validate that question types are provided and not empty
      if (!generationOptions?.questionTypes || generationOptions.questionTypes.length === 0) {
        console.error("Backend: Validation failed - no question types provided");
        return c.json(
          {
            error: "At least one question type must be selected for AI generation",
          },
          400
        );
      }

      // Document ownership already verified above for Format 2
      // For Format 1, verify document ownership only if we need to fetch content from the database
      // If textContent is already provided, we don't need to verify the document exists
      if (documentId && !requestBody.textContent) {
        const { data: documentOwner, error: docError } = await supabase
          .from("study_documents")
          .select("user_id")
          .eq("id", documentId)
          .single();

        if (docError || !documentOwner) {
          return c.json(
            {
              error: `Failed to verify document ownership or document not found for ID: ${documentId}`,
            },
            404
          );
        }

        if (documentOwner.user_id !== user.id) {
          return c.json(
            {
              error: `Forbidden: Document ${documentId} does not belong to this user.`,
            },
            403
          );
        }
      }

      console.log("🤖 Generating questions from AI...");
      console.log(`Quiz Generation: Requested ${numberOfQuestions} questions of types: [${generationOptions?.questionTypes?.join(", ") || "default"}]`);

      const aiGeneratedData = await generateQuestionsFromAI(
        textContent,
        numberOfQuestions,
        aiConfig,
        quizTitle,
        customPrompt,
        generationOptions?.questionTypes
      );

      if (
        !aiGeneratedData ||
        !aiGeneratedData.questions ||
        aiGeneratedData.questions.length === 0
      ) {
        return c.json(
          {
            error:
              "Failed to generate questions from AI or AI returned no questions.",
          },
          500
        );
      }

      // Log what types were actually generated
      const generatedTypes = aiGeneratedData.questions.map(q => q.type);
      const typeCounts = generatedTypes.reduce((acc, type) => {
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      console.log(`Quiz Generation: AI generated ${aiGeneratedData.questions.length} questions with types:`, typeCounts);

      // Ensure the number of questions matches the request, or handle discrepancies
      const generatedQs = aiGeneratedData.questions.slice(0, numberOfQuestions);
      if (generatedQs.length < numberOfQuestions) {
        console.warn(
          `AI generated ${generatedQs.length} questions, but ${numberOfQuestions} were requested.`
        );
        // Decide if this is an error or if we proceed with fewer questions
        if (generatedQs.length === 0) {
          return c.json({ error: "AI generated no usable questions." }, 500);
        }
      }

      console.log("💾 Saving quiz to database...");

      let finalStudyDocumentId: string | null = null;
      if (documentId) {
        // If documentId might be a comma-separated list from multiple docs, take the first one for linking.
        // Or, decide on a strategy for linking quizzes to multiple documents (e.g., a join table or array field).
        // For simplicity, let's assume we link to the first document if multiple are provided.
        const firstDocId = documentId.split(",")[0];
        if (firstDocId) {
          const uuidRegex =
            /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
          if (uuidRegex.test(firstDocId)) {
            const { data: existingDoc, error: docCheckError } = await supabase
              .from("study_documents")
              .select("id")
              .eq("id", firstDocId)
              .eq("user_id", user.id)
              .maybeSingle();

            if (docCheckError) {
              console.error(
                `Error checking for document ${firstDocId}:`,
                docCheckError.message
              );
            } else if (existingDoc) {
              finalStudyDocumentId = existingDoc.id;
            } else {
              console.warn(
                `Document with ID "${firstDocId}" not found for user "${user.id}". Proceeding with null study_document_id for quiz.`
              );
            }
          } else {
            console.warn(
              `Invalid UUID format for documentId: "${firstDocId}". Proceeding with null for study_document_id.`
            );
          }
        } else {
          console.warn(
            `No valid document ID found in "${documentId}". Proceeding with null for study_document_id.`
          );
        }
      }

      // Use Supabase instead of Drizzle ORM
      let savedQuiz;
      let quizError;

      const { data: firstAttemptQuiz, error: firstAttemptError } =
        await supabase
          .from("quizzes")
          .insert({
            name: quizTitle,
            study_document_id: finalStudyDocumentId, // Use validated and existing or null ID
            user_id: user.id,
          })
          .select()
          .single();

      savedQuiz = firstAttemptQuiz;
      quizError = firstAttemptError;

      if (quizError) {
        console.error("Initial error saving quiz:", quizError);
        // Check for specific foreign key violation on document_id
        if (
          quizError.code === "23503" &&
          quizError.message.includes("quizzes_document_id_fkey") &&
          finalStudyDocumentId !== null
        ) {
          console.warn(
            `Foreign key violation for document_id: ${finalStudyDocumentId}. Retrying quiz insert with null document_id.`
          );
          const { data: retryQuiz, error: retryError } = await supabase
            .from("quizzes")
            .insert({
              name: quizTitle,
              study_document_id: null, // Attempt with null
              user_id: user.id,
            })
            .select()
            .single();

          if (retryError) {
            console.error(
              "Error saving quiz on retry with null document_id:",
              retryError
            );
            // Keep the original error for the response if retry also fails
            quizError = retryError; // Or could be firstAttemptError, depending on desired behavior
            savedQuiz = null;
          } else {
            console.log(
              "Successfully saved quiz on retry with null document_id."
            );
            savedQuiz = retryQuiz;
            quizError = null; // Clear error as retry was successful
          }
        }
      }

      if (quizError || !savedQuiz) {
        console.error(
          "Final error saving quiz after potential retry:",
          quizError
        );
        return c.json({ error: "Failed to save quiz to database." }, 500);
      }

      console.log("💾 Attempting to save quiz questions to database...");

      // Check if quiz_questions table exists by attempting to insert
      const questionsToInsert = generatedQs.map((q) => {
        let correctAnswerValue: string | null = null;
        if (typeof q.correctAnswer === "string")
          correctAnswerValue = q.correctAnswer;
        else if (typeof q.correctAnswer === "boolean")
          correctAnswerValue = q.correctAnswer.toString();
        else if (Array.isArray(q.correctAnswer))
          correctAnswerValue = JSON.stringify(q.correctAnswer);
        return {
          quiz_id: savedQuiz.id,
          question_text: q.questionText,
          type: q.type,
          options: q.options ?? null,
          correct_answer: correctAnswerValue,
          explanation: q.explanation || null,
          user_id: user.id,
        };
      });

      let insertedQuestions = null;
      let questionsError = null;

      // Try to save questions if table exists
      if (questionsToInsert.length > 0) {
        try {
          const result = await supabase
            .from("quiz_questions")
            .insert(questionsToInsert)
            .select();

          insertedQuestions = result.data;
          questionsError = result.error;
        } catch (error: any) {
          console.warn(
            "Quiz questions table may not exist yet:",
            error.message
          );
          questionsError = error;
        }

        if (questionsError) {
          console.warn(
            "Could not save quiz questions (table may not exist):",
            questionsError
          );
          // Don't fail the entire operation - just save the quiz without questions for now
        }
      }

      console.log("✅ Quiz saved successfully!");

      // Create a simplified quiz response that matches what the frontend expects
      const fullQuiz = {
        id: savedQuiz.id.toString(),
        title: savedQuiz.name,
        questions: insertedQuestions
          ? insertedQuestions.map((q) => ({
              id: q.id.toString(),
              questionText: q.question_text,
              type: q.type,
              options: q.options,
              correctAnswer: q.correct_answer,
              explanation: q.explanation || undefined,
            }))
          : generatedQs.map((q, index) => ({
              id: `temp_${index}`,
              questionText: q.questionText,
              type: q.type,
              options: q.options,
              correctAnswer: q.correctAnswer,
              explanation: q.explanation || undefined,
            })),
      };

      // Return consistent format for both request types
      return c.json(
        {
          success: true,
          quiz: fullQuiz,
          quizId: savedQuiz.id.toString(),
          id: savedQuiz.id.toString(),
          name: savedQuiz.name,
        },
        201
      );
    } catch (error: any) {
      console.error("❌ Error in /generate quiz route:", error);
      console.error("Error stack:", error.stack);
      return c.json(
        { error: error.message || "An unexpected error occurred" },
        500
      );
    }
  }
);

// Route to fetch a specific quiz and its questions
quizRoutes.get("/:quizId", async (c: Context<{ Variables: AppVariables }>) => {
  const supabase = c.get("supabase");
  const user = c.get("user");
  const quizId = c.req.param("quizId");

  if (!quizId) {
    return c.json({ error: "Invalid quiz ID" }, 400);
  }

  try {
    // First get the quiz basic info
    const { data: quiz, error: quizError } = await supabase
      .from("quizzes")
      .select("*")
      .eq("id", quizId)
      .eq("user_id", user.id) // Ensure user owns the quiz
      .single();

    if (quizError || !quiz) {
      console.error("Error fetching quiz:", quizError);
      return c.json({ error: "Quiz not found or access denied" }, 404);
    }

    // Try to get quiz questions if the table exists
    try {
      const { data: questions, error: questionsError } = await supabase
        .from("quiz_questions")
        .select(
          "id, question_text, type, options, correct_answer, explanation, created_at"
        )
        .eq("quiz_id", quizId)
        .eq("user_id", user.id)
        .order("created_at", { ascending: true });

      if (!questionsError && questions) {
        quiz.quiz_questions = questions.map((q: any) => ({
          id: q.id,
          question_text: q.question_text,
          type: q.type,
          options: q.options,
          correct_answer: q.correct_answer,
          explanation: q.explanation,
          created_at: q.created_at,
        }));
      } else {
        console.warn(
          "Could not fetch quiz questions (table may not exist):",
          questionsError
        );
        quiz.quiz_questions = [];
      }
    } catch (error: any) {
      console.warn("Quiz questions table may not exist:", error.message);
      quiz.quiz_questions = [];
    }

    return c.json(quiz, 200);
  } catch (error: any) {
    console.error("Error fetching quiz by ID:", error);
    return c.json({ error: error.message || "Failed to fetch quiz" }, 500);
  }
});

// Route to fetch all quizzes for a specific document (owned by the user)
quizRoutes.get(
  "/document/:documentId",
  async (c: Context<{ Variables: AppVariables }>) => {
    const supabase = c.get("supabase");
    const user = c.get("user");
    const documentId = c.req.param("documentId");

    if (!documentId) {
      return c.json({ error: "Invalid document ID" }, 400);
    }

    try {
      const { data: docData, error: docError } = await supabase
        .from("study_documents")
        .select("id")
        .eq("id", documentId)
        .eq("user_id", user.id)
        .single();

      if (docError || !docData) {
        return c.json({ error: "Document not found or access denied." }, 404);
      }

      const { data: quizzesData, error: quizzesError } = await supabase
        .from("quizzes")
        .select("id, name, created_at, study_document_id")
        .eq("study_document_id", documentId)
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (quizzesError) {
        console.error("Error fetching quizzes for document:", quizzesError);
        return c.json(
          { error: "Failed to fetch quizzes for the document" },
          500
        );
      }

      return c.json(quizzesData || [], 200);
    } catch (error: any) {
      console.error("Error fetching quizzes by document ID:", error);
      return c.json({ error: error.message || "Failed to fetch quizzes" }, 500);
    }
  }
);

// Add a testing endpoint to verify the router is working properly
quizRoutes.get("/test", async (c: Context) => {
  console.log("Test endpoint hit successfully!");
  return c.json({ status: "success", message: "Quiz routes are working!" });
});

// Route to update SRS data for a quiz question
quizRoutes.patch(
  "/questions/:questionId/srs",
  async (c: Context<{ Variables: AppVariables }>) => {
    const supabase = c.get("supabase");
    const user = c.get("user");
    const questionId = c.req.param("questionId");

    try {
      const body = await c.req.json();
      const {
        srs_level,
        due_at,
        last_reviewed_at,
        srs_interval,
        srs_ease_factor,
        srs_repetitions,
        srs_correct_streak,
      } = body;

      if (!questionId) {
        return c.json({ error: "Question ID is required" }, 400);
      }

      // Verify the question belongs to the user
      const { data: questionCheck, error: checkError } = await supabase
        .from("quiz_questions")
        .select("user_id")
        .eq("id", questionId)
        .single();

      if (checkError || !questionCheck) {
        return c.json({ error: "Question not found" }, 404);
      }

      if (questionCheck.user_id !== user.id) {
        return c.json(
          { error: "Unauthorized: Question does not belong to this user" },
          403
        );
      }

      // Update the SRS fields
      const updateData: any = {};
      if (srs_level !== undefined) updateData.srs_level = srs_level;
      if (due_at !== undefined) updateData.due_at = due_at;
      if (last_reviewed_at !== undefined)
        updateData.last_reviewed_at = last_reviewed_at;
      if (srs_interval !== undefined) updateData.srs_interval = srs_interval;
      if (srs_ease_factor !== undefined)
        updateData.srs_ease_factor = srs_ease_factor;
      if (srs_repetitions !== undefined)
        updateData.srs_repetitions = srs_repetitions;
      if (srs_correct_streak !== undefined)
        updateData.srs_correct_streak = srs_correct_streak;

      if (Object.keys(updateData).length === 0) {
        return c.json({ error: "No SRS fields provided for update" }, 400);
      }

      const { data: updatedQuestion, error: updateError } = await supabase
        .from("quiz_questions")
        .update(updateData)
        .eq("id", questionId)
        .eq("user_id", user.id)
        .select()
        .single();

      if (updateError) {
        console.error("Error updating question SRS:", updateError);
        return c.json(
          {
            error: "Failed to update question SRS",
            details: updateError.message,
          },
          500
        );
      }

      return c.json({ success: true, question: updatedQuestion }, 200);
    } catch (error: any) {
      console.error("Error in SRS update:", error);
      return c.json(
        { error: "An unexpected error occurred", details: error.message },
        500
      );
    }
  }
);

// Route to create a new quiz manually (without AI generation)
quizRoutes.post("/", async (c: Context<{ Variables: AppVariables }>) => {
  const supabase = c.get("supabase");
  const user = c.get("user");

  try {
    const body = await c.req.json();
    const { name, description, study_document_id } = body;

    if (!name || typeof name !== "string" || name.trim().length === 0) {
      return c.json(
        { error: "Name is required and must be a non-empty string" },
        400
      );
    }

    const quizData = {
      name: name.trim(),
      description: description || null,
      study_document_id: study_document_id || null,
      user_id: user.id,
    };

    const { data: quiz, error: quizError } = await supabase
      .from("quizzes")
      .insert(quizData)
      .select("id, name")
      .single();

    if (quizError) {
      console.error("Error creating quiz:", quizError);
      return c.json(
        { error: "Failed to create quiz", details: quizError.message },
        500
      );
    }

    return c.json({ id: quiz.id, name: quiz.name }, 201);
  } catch (error: any) {
    console.error("Error in manual quiz creation:", error);
    return c.json(
      { error: "An unexpected error occurred", details: error.message },
      500
    );
  }
});

// Route to fetch all quizzes for the authenticated user
quizRoutes.get("/", async (c: Context<{ Variables: AppVariables }>) => {
  const user = c.get("user");
  console.log(`quizRoutes: GET / main handler reached. User ID: ${user?.id}`); // Log 5
  console.log("quizRoutes: GET / handler triggered"); // Added for debugging
  try {
    const supabase = c.get("supabase");
    const user = c.get("user");

    // Defensive checks, though middleware should handle auth and set these
    if (!supabase) {
      console.error(
        "GET /api/quizzes/ handler: Supabase client not found in context."
      );
      return c.json(
        {
          error:
            "Internal Server Configuration Error: Supabase client not available.",
        },
        500
      );
    }
    if (!user || !user.id) {
      console.error(
        "GET /api/quizzes/ handler: User not found in context or user ID missing."
      );
      return c.json(
        {
          error: "Authentication Error: User information not available.",
          details: "User context is invalid or missing.",
        },
        500
      );
    }

    const { data: quizzesData, error: quizzesError } = await supabase
      .from("quizzes")
      .select("id, name, created_at, study_document_id")
      .eq("user_id", user.id)
      .order("created_at", { ascending: false });

    if (quizzesError) {
      console.error("Error fetching user quizzes from Supabase:", quizzesError);
      return c.json(
        {
          error: "Failed to fetch user quizzes",
          details:
            quizzesError.message || "Database query encountered an error.",
        },
        500
      );
    }

    return c.json({ quizzes: quizzesData || [] }, 200);
  } catch (error: any) {
    console.error("Unexpected error in GET /api/quizzes/ handler:", error);
    return c.json(
      {
        error: "An unexpected server error occurred while fetching quizzes.",
        details: error.message || "Unknown internal server error.",
      },
      500
    );
  }
});

// Route to delete a quiz and its questions
quizRoutes.delete(
  "/:quizId",
  async (c: Context<{ Variables: AppVariables }>) => {
    const supabase = c.get("supabase");
    const user = c.get("user");
    const quizId = c.req.param("quizId");

    if (!quizId) {
      return c.json({ error: "Invalid quiz ID" }, 400);
    }

    try {
      // First, delete all questions associated with this quiz and user
      const { error: questionsDeleteError } = await supabase
        .from("quiz_questions")
        .delete()
        .eq("quiz_id", quizId)
        .eq("user_id", user.id); // Ensure user owns the questions

      if (questionsDeleteError) {
        console.error("Error deleting quiz questions:", questionsDeleteError);
        return c.json(
          {
            error: "Failed to delete quiz questions",
            details: questionsDeleteError.message,
          },
          500
        );
      }

      // Then, delete the quiz itself
      const { error: quizDeleteError } = await supabase
        .from("quizzes")
        .delete()
        .eq("id", quizId)
        .eq("user_id", user.id); // Ensure user owns the quiz

      if (quizDeleteError) {
        console.error("Error deleting quiz:", quizDeleteError);
        return c.json(
          { error: "Failed to delete quiz", details: quizDeleteError.message },
          500
        );
      }

      return c.json(
        {
          success: true,
          message: "Quiz and associated questions deleted successfully.",
        },
        200
      );
    } catch (error: any) {
      console.error("Error deleting quiz:", error);
      return c.json({ error: error.message || "Failed to delete quiz" }, 500);
    }
  }
);

// Custom Not Found handler for quizRoutes
quizRoutes.notFound((c) => {
  console.error(
    `[quizRoutes] Not Found: Path ${c.req.path} with method ${c.req.method} was not matched.`
  );
  return c.json(
    { error: "Quiz route not found", path: c.req.path, method: c.req.method },
    404
  );
});

// Route to add a batch of questions to an existing quiz
quizRoutes.post('/:quizId/questions/batch', async (c: Context<{ Variables: AppVariables }>) => {
  const supabase = c.get('supabase');
  const user = c.get('user');
  const quizId = c.req.param('quizId');

  try {
    const { questions: questionsPayload } = await c.req.json<{ questions: QuizQuestionBatchInsertPayload[] }>();

    if (!quizId) {
      return c.json({ error: 'Quiz ID is required' }, 400);
    }

    if (!Array.isArray(questionsPayload) || questionsPayload.length === 0) {
      return c.json({ error: 'Questions payload must be a non-empty array' }, 400);
    }

    // Verify user owns the quiz
    const { data: quiz, error: quizError } = await supabase
      .from('quizzes')
      .select('id, user_id')
      .eq('id', quizId)
      .single();

    if (quizError || !quiz) {
      return c.json({ error: 'Quiz not found' }, 404);
    }
    if (quiz.user_id !== user.id) {
      return c.json({ error: 'Forbidden: You do not own this quiz' }, 403);
    }

    // Prepare questions for insertion
    const questionsToInsert = questionsPayload.map(q => ({
      question_text: q.question_text,
      type: q.type, // Assuming q.type is already a valid enum string
      options: q.options || null,
      correct_answer: q.correct_answer || null,
      explanation: q.explanation || null,
      quiz_id: quizId,
      user_id: user.id,
    }));

    const { data: insertedQuestions, error: insertError } = await supabase
      .from('quiz_questions')
      .insert(questionsToInsert)
      .select();

    if (insertError) {
      console.error('Error inserting batch questions:', insertError);
      return c.json({ error: 'Failed to add questions to quiz', details: insertError.message }, 500);
    }

    return c.json(insertedQuestions, 201);
  } catch (error: any) {
    console.error('Error in /:quizId/questions/batch route:', error);
    return c.json({ error: 'An unexpected error occurred', details: error.message }, 500);
  }
});

// Route to fetch a specific quiz and its questions
quizRoutes.get("/:quizId", async (c: Context<{ Variables: AppVariables }>) => {
  const supabase = c.get("supabase");
  const user = c.get("user");
  const quizId = c.req.param("quizId");

  if (!quizId) {
    return c.json({ error: "Invalid quiz ID" }, 400);
  }

  try {
    // Fetch the quiz
    const { data: quiz, error: quizError } = await supabase
      .from("quizzes")
      .select("*")
      .eq("id", quizId)
      .single();

    if (quizError) {
      console.error("Error fetching quiz:", quizError);
      return c.json(
        { error: "Failed to fetch quiz", details: quizError.message },
        quizError.code === "PGRST116" ? 404 : 500
      );
    }

    // Check if user owns the quiz
    if (quiz.user_id !== user.id) {
      return c.json(
        { error: "You do not have permission to access this quiz" },
        403
      );
    }

    // Fetch questions for this quiz
    const { data: questions, error: questionsError } = await supabase
      .from("quiz_questions")
      .select("*")
      .eq("quiz_id", quizId)
      .eq("user_id", user.id)
      .order("created_at");

    if (questionsError) {
      console.error("Error fetching quiz questions:", questionsError);
      return c.json(
        {
          error: "Failed to fetch quiz questions",
          details: questionsError.message,
        },
        500
      );
    }

    // Return the quiz with its questions
    return c.json({
      ...quiz,
      questions: questions || [],
    });
  } catch (error: any) {
    console.error("Error fetching quiz by ID:", error);
    return c.json({ error: error.message || "Failed to fetch quiz" }, 500);
  }
});

// Route to track quiz completion
quizRoutes.post(
  "/:quizId/complete",
  async (c: Context<{ Variables: AppVariables }>) => {
    const supabase = c.get("supabase");
    const user = c.get("user");
    const quizId = c.req.param("quizId");

    if (!quizId) {
      return c.json({ error: "Quiz ID is required" }, 400);
    }

    try {
      const body = await c.req.json();
      const completionData: QuizCompletionData = body;

      // Validate required fields
      if (!completionData.score || !completionData.questions_answered) {
        return c.json({ error: "Score and questions answered are required" }, 400);
      }

      // Verify the quiz belongs to the user
      const { data: quiz, error: quizError } = await supabase
        .from("quizzes")
        .select("user_id")
        .eq("id", quizId)
        .single();

      if (quizError || !quiz) {
        return c.json({ error: "Quiz not found" }, 404);
      }

      if (quiz.user_id !== user.id) {
        return c.json(
          { error: "Unauthorized: Quiz does not belong to this user" },
          403
        );
      }

      // Create completion record
      const completionInsert: UserCompletionInsert = {
        user_id: user.id,
        quiz_id: quizId,
        completion_type: "quiz",
        completed_at: new Date().toISOString(),
        score: completionData.score,
        time_spent_minutes: completionData.time_spent_minutes,
        questions_answered: completionData.questions_answered,
        correct_answers: completionData.correct_answers,
        metadata: completionData.metadata || null,
      };

      const { data: completion, error: insertError } = await supabase
        .from("user_completions")
        .insert(completionInsert)
        .select()
        .single();

      if (insertError) {
        console.error("Error recording quiz completion:", insertError);
        return c.json(
          { error: "Failed to record completion", details: insertError.message },
          500
        );
      }

      return c.json(completion, 201);
    } catch (error: any) {
      console.error("Error in quiz completion endpoint:", error);
      return c.json(
        { error: "An unexpected error occurred", details: error.message },
        500
      );
    }
  }
);

// Route to get completion statistics for the user
quizRoutes.get(
  "/stats/completions",
  async (c: Context<{ Variables: AppVariables }>) => {
    const supabase = c.get("supabase");
    const user = c.get("user");

    try {
      // Get all user completions
      const { data: completions, error: completionsError } = await supabase
        .from("user_completions")
        .select("*")
        .eq("user_id", user.id)
        .order("completed_at", { ascending: false });

      if (completionsError) {
        console.error("Error fetching completions:", completionsError);
        return c.json(
          { error: "Failed to fetch completions", details: completionsError.message },
          500
        );
      }

      const allCompletions = completions || [];
      const quizCompletions = allCompletions.filter(c => c.completion_type === "quiz");
      const flashcardCompletions = allCompletions.filter(c => c.completion_type === "flashcard_set");

      // Calculate statistics
      const stats: CompletionStats = {
        total_completions: allCompletions.length,
        quiz_completions: quizCompletions.length,
        flashcard_completions: flashcardCompletions.length,
        average_quiz_score: quizCompletions.length > 0
          ? Math.round(quizCompletions.reduce((sum, c) => sum + (c.score || 0), 0) / quizCompletions.length)
          : 0,
        total_study_time_minutes: allCompletions.reduce((sum, c) => sum + (c.time_spent_minutes || 0), 0),
        best_quiz_score: quizCompletions.length > 0
          ? Math.max(...quizCompletions.map(c => c.score || 0))
          : 0,
        recent_completions: allCompletions.slice(0, 10),
        completion_streak: calculateCompletionStreak(allCompletions),
        this_week_completions: getCompletionsInTimeRange(allCompletions, 7),
        this_month_completions: getCompletionsInTimeRange(allCompletions, 30),
      };

      return c.json(stats);
    } catch (error: any) {
      console.error("Error fetching completion stats:", error);
      return c.json(
        { error: "An unexpected error occurred", details: error.message },
        500
      );
    }
  }
);

// Route to get user's quiz completion data
quizRoutes.get(
  "/:quizId/user-completion",
  async (c: Context<{ Variables: AppVariables }>) => {
    const supabase = c.get("supabase");
    const user = c.get("user");
    const quizId = c.req.param("quizId");

    if (!quizId) {
      return c.json({ error: "Quiz ID is required" }, 400);
    }

    try {
      // Fetch user's completion data for this quiz
      const { data, error } = await supabase
        .from("quiz_completions")
        .select("*")
        .eq("quiz_id", quizId)
        .eq("user_id", user.id)
        .single();

      if (error) {
        throw error;
      }

      return c.json(
        { success: true, completionData: data },
        200
      );
    } catch (error: any) {
      console.error("Error fetching user completion data:", error);
      return c.json(
        { error: "Failed to fetch user completion data", details: error.message },
        500
      );
    }
  }
);

// Route to get user's completion history with filtering
quizRoutes.get(
  "/completions",
  async (c: Context<{ Variables: AppVariables }>) => {
    const supabase = c.get("supabase");
    const user = c.get("user");

    try {
      const queryParams = c.req.query();
      const filters: CompletionFilters = {
        completion_type: queryParams.type as 'quiz' | 'flashcard_set' | undefined,
        quiz_id: queryParams.quiz_id,
        flashcard_set_id: queryParams.flashcard_set_id,
        min_score: queryParams.min_score ? parseInt(queryParams.min_score) : undefined,
        limit: queryParams.limit ? parseInt(queryParams.limit) : 50,
        offset: queryParams.offset ? parseInt(queryParams.offset) : 0,
      };

      if (queryParams.start_date && queryParams.end_date) {
        filters.date_range = {
          start: queryParams.start_date,
          end: queryParams.end_date,
        };
      }

      // Build query
      let query = supabase
        .from("user_completions")
        .select("*")
        .eq("user_id", user.id);

      if (filters.completion_type) {
        query = query.eq("completion_type", filters.completion_type);
      }

      if (filters.quiz_id) {
        query = query.eq("quiz_id", filters.quiz_id);
      }

      if (filters.flashcard_set_id) {
        query = query.eq("flashcard_set_id", filters.flashcard_set_id);
      }

      if (filters.min_score) {
        query = query.gte("score", filters.min_score);
      }

      if (filters.date_range) {
        query = query
          .gte("completed_at", filters.date_range.start)
          .lte("completed_at", filters.date_range.end);
      }

      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      query = query.order("completed_at", { ascending: false });

      const { data: completions, error: completionsError } = await query;

      if (completionsError) {
        console.error("Error fetching filtered completions:", completionsError);
        return c.json(
          { error: "Failed to fetch completions", details: completionsError.message },
          500
        );
      }

      return c.json(completions || []);
    } catch (error: any) {
      console.error("Error in completions endpoint:", error);
      return c.json(
        { error: "An unexpected error occurred", details: error.message },
        500
      );
    }
  }
);

// Helper functions for completion statistics
function calculateCompletionStreak(completions: any[]): number {
  if (completions.length === 0) return 0;

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  let streak = 0;
  let currentDate = new Date(today);
  
  for (let i = 0; i < 30; i++) { // Check last 30 days
    const dayCompletions = completions.filter(c => {
      const completionDate = new Date(c.completed_at);
      completionDate.setHours(0, 0, 0, 0);
      return completionDate.getTime() === currentDate.getTime();
    });
    
    if (dayCompletions.length > 0) {
      streak++;
    } else if (streak > 0) {
      break; // Streak is broken
    }
    
    currentDate.setDate(currentDate.getDate() - 1);
  }
  
  return streak;
}

function getCompletionsInTimeRange(completions: any[], days: number): number {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);
  
  return completions.filter(c => new Date(c.completed_at) >= cutoffDate).length;
}

export default quizRoutes;
