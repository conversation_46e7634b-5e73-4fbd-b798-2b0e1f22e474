import express, { type Express } from "express";
import fs from "fs";
import path from "path";
import { createServer as createViteServer } from "vite";
import { fileURLToPath } from "url";

// Get current file's directory in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export function log(message: string) {
  const now = new Date();
  const timeStr = now.toLocaleTimeString();
  console.log(`${timeStr} [express] ${message}`);
}

export async function setupVite(app: Express) {
  if (process.env.NODE_ENV === "production") {
    log("Production mode detected, skipping Vite setup");
    return;
  }

  log("Setting up Vite dev middleware...");
  
  try {
    const vite = await createViteServer({
      server: { middlewareMode: true },
      appType: "spa",
      // Pass any additional Vite config options
    });

    app.use(vite.middlewares);
    log("Vite dev middleware set up successfully");
  } catch (e: any) {
    log(`Error setting up Vite: ${e.message}`);
    if (e.stack) log(e.stack);
    process.exit(1);
  }
}

export function serveStatic(app: Express) {
  // In production, serve the built frontend from the dist/public directory
  const distPath = path.resolve(process.cwd(), "dist/public");

  // Check if the dist/public directory exists
  if (!fs.existsSync(distPath)) {
    log(`Warning: Static files directory ${distPath} does not exist!`);
    log('Make sure to build the client first with npm run build');
    return;
  }

  log(`Serving static files from ${distPath}`);

  // Security headers middleware for static files
  app.use((req, res, next) => {
    // Skip API routes - they have their own security headers
    if (req.path.startsWith("/api")) {
      return next();
    }

    // Security headers for static content
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

    // Content Security Policy for production
    if (process.env.NODE_ENV === 'production') {
      res.setHeader('Content-Security-Policy',
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://replit.com; " +
        "style-src 'self' 'unsafe-inline'; " +
        "img-src 'self' data: https:; " +
        "font-src 'self' data:; " +
        "connect-src 'self' https://hrdjfukhzbzksqaupqie.supabase.co https://openrouter.ai wss:; " +
        "frame-src 'none';"
      );
    }

    next();
  });

  // Serve static files with optimized caching
  app.use(express.static(distPath, {
    maxAge: process.env.NODE_ENV === 'production' ? '1y' : 0,
    etag: true,
    index: false, // Don't automatically serve index.html, we'll handle that below
    setHeaders: (res, path) => {
      // Set specific cache headers for different file types
      if (path.endsWith('.html')) {
        res.setHeader('Cache-Control', 'no-cache');
      } else if (path.match(/\.(js|css|woff2?|ttf|eot)$/)) {
        res.setHeader('Cache-Control', 'public, max-age=********, immutable');
      }
    }
  }));

  // For SPA routing - all non-API routes should serve the index.html
  app.get("*", (req, res, next) => {
    // Skip API routes and let them be handled by their respective handlers
    if (req.path.startsWith("/api")) {
      return next();
    }

    // Serve the index.html for client-side routing
    const indexPath = path.join(distPath, "index.html");
    if (fs.existsSync(indexPath)) {
      // Set no-cache for index.html to ensure updates are picked up
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.sendFile(indexPath);
    } else {
      log(`Warning: index.html not found at ${indexPath}`);
      res.status(404).send('Frontend build not found');
    }
  });
}
