#!/usr/bin/env node

// <PERSON><PERSON><PERSON> to create the user_ai_credentials table
import { createClient } from '@supabase/supabase-js';

// Environment variables
const SUPABASE_URL = 'https://hrdjfukhzbzksqaupqie.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

const createTableSQL = `
-- Create table for securely storing user AI provider credentials
CREATE TABLE IF NOT EXISTS user_ai_credentials (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    provider VARCHAR(50) NOT NULL,
    encrypted_api_key TEXT NOT NULL,
    encryption_iv VARCHAR(32) NOT NULL,
    encryption_tag VARCHAR(32) NOT NULL,
    base_url TEXT NOT NULL,
    extraction_model VARCHAR(100),
    generation_model VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one credential set per user per provider
    UNIQUE(user_id, provider)
);

-- Enable Row Level Security
ALTER TABLE user_ai_credentials ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only access their own credentials
CREATE POLICY "Users can view own AI credentials" ON user_ai_credentials
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own AI credentials" ON user_ai_credentials
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own AI credentials" ON user_ai_credentials
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own AI credentials" ON user_ai_credentials
    FOR DELETE USING (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_ai_credentials_user_id ON user_ai_credentials(user_id);
CREATE INDEX IF NOT EXISTS idx_user_ai_credentials_provider ON user_ai_credentials(provider);
CREATE INDEX IF NOT EXISTS idx_user_ai_credentials_user_provider ON user_ai_credentials(user_id, provider);
`;

async function createTable() {
  console.log('🗄️ Creating user_ai_credentials table...');
  
  try {
    // Split the SQL into individual statements and execute them
    const statements = createTableSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    for (const statement of statements) {
      console.log('Executing:', statement.substring(0, 50) + '...');
      
      const { error } = await supabase.rpc('exec_sql', { 
        sql: statement + ';' 
      });
      
      if (error) {
        console.error('❌ SQL Error:', error);
        return false;
      }
    }
    
    console.log('✅ Table created successfully!');
    
    // Test if the table exists now
    console.log('🔍 Verifying table creation...');
    const { data: tableData, error: tableError } = await supabase
      .from('user_ai_credentials')
      .select('id')
      .limit(1);
    
    if (tableError) {
      console.error('❌ Table verification failed:', tableError);
      return false;
    }
    
    console.log('✅ Table verified successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Error creating table:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting table creation...\n');
  
  const success = await createTable();
  
  if (success) {
    console.log('\n🎉 Table creation completed successfully!');
    console.log('The user_ai_credentials table is now ready for use.');
  } else {
    console.log('\n❌ Table creation failed. Please check the errors above.');
  }
}

main().catch(console.error);
