# Specifies the Nix environment and Replit modules to use.
# 'nodejs-20' provides the Node.js runtime.
# 'web' is necessary for Replit to serve your application on standard HTTP/HTTPS ports.
# Removed 'postgresql-16' as your Drizzle and Supabase configurations point to an external Supabase DB.
modules = ["nodejs-20", "web"]

# Command to execute when the "Run" button in the Replit IDE is pressed.
# 'npm run dev' typically starts your development servers (frontend and backend).
run = "npm run dev"

# Files and directories to hide from the Replit file explorer.
hidden = [".config", ".git", "generated-icon.png", "node_modules", "dist"]

[nix]
# Specifies the Nix channel for environment reproducibility.
channel = "stable-24_05"

[deployment]
# Configures the deployment target on Replit.
deploymentTarget = "autoscale"
build = ["sh", "-c", "npm run build"]
run = ["sh", "-c", "NODE_ENV=production PORT=80 tsx server/index.ts"]

# Health check endpoint for monitoring
healthcheck = "/api/health"

# Environment variables for production deployment
[deployment.env]
NODE_ENV = "production"
PORT = "80"

# SPA Fallback: Rewrites all non-file paths to /index.html for client-side routing.
# Your server/index.ts also handles this, but this Replit rule can act as a fallback.
[[deployment.rewrites]]
from = "/*"
to = "/index.html"

# CORS Headers:
# Your application (server/index.ts) already configures CORS using `app.use(cors(...))`.
# The application-level CORS is more specific and generally preferred.
# These global Replit-level headers might be redundant or overly permissive.
# Consider removing these if your application's CORS handling is sufficient.
[[deployment.responseHeaders]]
path = "/*"
name = "Access-Control-Allow-Origin"
value = "*"

[[deployment.responseHeaders]]
path = "/*"
name = "Access-Control-Allow-Methods"
value = "GET, POST, PUT, DELETE, OPTIONS"

[[deployment.responseHeaders]]
path = "/*"
name = "Access-Control-Allow-Headers"
value = "Content-Type, Authorization"

[[ports]]
localPort = 80
externalPort = 80

[[ports]]
localPort = 3000
externalPort = 3000

[[ports]]
localPort = 3001
externalPort = 3001

[[ports]]
localPort = 5000
externalPort = 5001

[[ports]]
localPort = 24678
externalPort = 24678

# Workflow for the "Run" button in the Replit IDE.
[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"
[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Start application"

[[workflows.workflow]]
name = "Start application"
author = "agent"
[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev" # This executes your development script.
waitForPort = 3000    # Waits for the Vite frontend dev server to be ready on port 3000.
