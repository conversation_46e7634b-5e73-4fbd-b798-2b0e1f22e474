#!/usr/bin/env node

// <PERSON><PERSON><PERSON> to apply the user_ai_credentials migration
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Environment variables
const SUPABASE_URL = 'https://hrdjfukhzbzksqaupqie.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function applyMigration() {
  console.log('📄 Reading migration file...');
  
  try {
    const migrationSQL = fs.readFileSync('supabase/migrations/20250602_create_user_ai_credentials.sql', 'utf8');
    console.log('✅ Migration file loaded');
    
    console.log('🗄️ Applying migration to database...');
    
    // Execute the migration SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSQL });
    
    if (error) {
      console.error('❌ Migration failed:', error);
      return false;
    }
    
    console.log('✅ Migration applied successfully!');
    
    // Test if the table exists now
    console.log('🔍 Verifying table creation...');
    const { data: tableData, error: tableError } = await supabase
      .from('user_ai_credentials')
      .select('id')
      .limit(1);
    
    if (tableError) {
      console.error('❌ Table verification failed:', tableError);
      return false;
    }
    
    console.log('✅ Table verified successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Error applying migration:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting migration application...\n');
  
  const success = await applyMigration();
  
  if (success) {
    console.log('\n🎉 Migration completed successfully!');
    console.log('The user_ai_credentials table is now ready for use.');
  } else {
    console.log('\n❌ Migration failed. Please check the errors above.');
  }
}

main().catch(console.error);
