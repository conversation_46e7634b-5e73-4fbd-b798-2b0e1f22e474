#!/usr/bin/env node

// Test script to verify credentials API functionality
import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

// Environment variables
const SUPABASE_URL = 'https://hrdjfukhzbzksqaupqie.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Encryption setup
const ENCRYPTION_KEY = 'default-key-change-in-production-32-chars';
const ALGORITHM = 'aes-256-cbc';

const getEncryptionKey = () => {
  return crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
};

function encryptApiKey(apiKey) {
  const iv = crypto.randomBytes(16);
  const key = getEncryptionKey();
  const cipher = crypto.createCipher(ALGORITHM, key);
  
  let encrypted = cipher.update(apiKey, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  // For CBC mode, we don't have auth tag, so we'll use a hash for integrity
  const tag = crypto.createHmac('sha256', key).update(encrypted + iv.toString('hex')).digest('hex');
  
  return {
    encrypted,
    iv: iv.toString('hex'),
    tag
  };
}

function decryptApiKey(encryptedData) {
  const iv = Buffer.from(encryptedData.iv, 'hex');
  const key = getEncryptionKey();
  
  // Verify integrity using HMAC
  const expectedTag = crypto.createHmac('sha256', key).update(encryptedData.encrypted + encryptedData.iv).digest('hex');
  if (expectedTag !== encryptedData.tag) {
    throw new Error('Data integrity check failed');
  }
  
  const decipher = crypto.createDecipher(ALGORITHM, key);
  
  let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
}

async function testEncryption() {
  console.log('🔐 Testing encryption/decryption...');
  
  const testApiKey = 'sk-test-api-key-12345';
  console.log('Original API key:', testApiKey);
  
  try {
    const encrypted = encryptApiKey(testApiKey);
    console.log('Encrypted data:', encrypted);
    
    const decrypted = decryptApiKey(encrypted);
    console.log('Decrypted API key:', decrypted);
    
    if (testApiKey === decrypted) {
      console.log('✅ Encryption/decryption test passed!');
      return true;
    } else {
      console.log('❌ Encryption/decryption test failed!');
      return false;
    }
  } catch (error) {
    console.error('❌ Encryption test error:', error);
    return false;
  }
}

async function testDatabaseConnection() {
  console.log('🗄️ Testing database connection...');

  try {
    const { data, error } = await supabase
      .from('user_ai_credentials')
      .select('id')
      .limit(1);

    if (error) {
      console.error('❌ Database connection error:', error);
      return false;
    }

    console.log('✅ Database connection successful!');
    return true;
  } catch (error) {
    console.error('❌ Database test error:', error);
    return false;
  }
}

async function testCredentialsStorage() {
  console.log('💾 Testing credentials storage...');

  const testUserId = '21716172-c5ec-4011-97e5-a0dcc2bfc27e'; // Real user ID from database
  const testProvider = 'test-provider';
  const testApiKey = 'sk-test-key-12345';
  
  try {
    // Encrypt the API key
    const encryptedKey = encryptApiKey(testApiKey);
    
    // Store in database
    const { error: insertError } = await supabase
      .from('user_ai_credentials')
      .upsert({
        user_id: testUserId,
        provider: testProvider,
        encrypted_api_key: encryptedKey.encrypted,
        encryption_iv: encryptedKey.iv,
        encryption_tag: encryptedKey.tag,
        base_url: 'https://test.example.com',
        extraction_model: 'test-extraction-model',
        generation_model: 'test-generation-model',
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id,provider'
      });

    if (insertError) {
      console.error('❌ Insert error:', insertError);
      return false;
    }
    
    console.log('✅ Credentials stored successfully!');
    
    // Retrieve and decrypt
    const { data, error: selectError } = await supabase
      .from('user_ai_credentials')
      .select('*')
      .eq('user_id', testUserId)
      .eq('provider', testProvider)
      .single();

    if (selectError) {
      console.error('❌ Select error:', selectError);
      return false;
    }
    
    const decryptedApiKey = decryptApiKey({
      encrypted: data.encrypted_api_key,
      iv: data.encryption_iv,
      tag: data.encryption_tag
    });
    
    if (decryptedApiKey === testApiKey) {
      console.log('✅ Credentials retrieval and decryption successful!');
      
      // Clean up test data
      await supabase
        .from('user_ai_credentials')
        .delete()
        .eq('user_id', testUserId)
        .eq('provider', testProvider);
      
      return true;
    } else {
      console.log('❌ Decrypted API key does not match original!');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Credentials storage test error:', error);
    return false;
  }
}

async function runTests() {
  console.log('🧪 Running credentials API tests...\n');
  
  const encryptionTest = await testEncryption();
  console.log('');
  
  const dbTest = await testDatabaseConnection();
  console.log('');
  
  const storageTest = await testCredentialsStorage();
  console.log('');
  
  console.log('📊 Test Results:');
  console.log('- Encryption/Decryption:', encryptionTest ? '✅ PASS' : '❌ FAIL');
  console.log('- Database Connection:', dbTest ? '✅ PASS' : '❌ FAIL');
  console.log('- Credentials Storage:', storageTest ? '✅ PASS' : '❌ FAIL');
  
  if (encryptionTest && dbTest && storageTest) {
    console.log('\n🎉 All tests passed! The credentials API should work correctly.');
  } else {
    console.log('\n❌ Some tests failed. Please check the errors above.');
  }
}

runTests().catch(console.error);
