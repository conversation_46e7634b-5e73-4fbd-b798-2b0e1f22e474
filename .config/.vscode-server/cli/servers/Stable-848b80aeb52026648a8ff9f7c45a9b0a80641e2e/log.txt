*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[16:24:33] 




[16:24:33] Extension host agent started.
[16:24:33] Started initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[16:24:33] ComputeTargetPlatform: linux-x64
[16:24:33] Completed initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[16:24:33] [<unknown>][8c2576f7][ExtensionHostConnection] New connection established.
[16:24:33] [<unknown>][5a43c97b][ManagementConnection] New connection established.
[16:24:33] [<unknown>][8c2576f7][ExtensionHostConnection] <3319> Launched Extension Host Process.
[16:24:35] ComputeTargetPlatform: linux-x64
[16:24:35] Getting Manifest... github.vscode-pull-request-github
[16:24:35] Installing extension: github.vscode-pull-request-github {
  installPreReleaseVersion: false,
  donotIncludePackAndDependencies: true,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' }
}
[16:24:35] Installing the extension without checking dependencies and pack github.vscode-pull-request-github
[16:24:37] Extension signature verification result for github.vscode-pull-request-github: Success. Internal Code: 0. Executed: true. Duration: 1713ms.
[16:24:38] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0: github.vscode-pull-request-github
[16:24:38] Renamed to /home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0
[16:24:38] Extension installed successfully: github.vscode-pull-request-github file:///home/<USER>/.vscode-server/extensions/extensions.json
[16:24:38] Downloaded extension to file:///home/<USER>/.vscode-server/data/CachedExtensionVSIXs/120a1599-4772-4e96-a642-e0261511475d
[16:24:38] Installing extension: github.copilot-chat {
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' }
}
[16:24:38] Getting Manifest... github.copilot
[16:24:38] Installing extension: github.copilot {
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' },
  pinned: false,
  installGivenVersion: false,
  context: { dependecyOrPackExtensionInstall: true }
}
[16:24:39] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2: github.copilot-chat
[16:24:39] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2
[16:24:40] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 1129ms.
[16:24:41] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.326.0: github.copilot
[16:24:41] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.326.0
[16:24:41] Extension installed successfully: github.copilot-chat file:///home/<USER>/.vscode-server/extensions/extensions.json
[16:24:41] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
[16:24:46] Downloaded extension to file:///home/<USER>/.vscode-server/data/CachedExtensionVSIXs/534cec37-1ae5-44f8-b1d9-c49d363d57bd
[16:24:46] Installing extension: github.copilot {
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' }
}
[16:24:46] Getting Manifest... github.copilot-chat
[16:24:47] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.326.1596: github.copilot
[16:24:47] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.326.1596
[16:24:47] Marked extension as removed github.copilot-1.326.0
[16:24:47] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
[16:25:19] Getting Manifest... christian-kohler.npm-intellisense
[16:25:19] Installing extension: christian-kohler.npm-intellisense {
  isMachineScoped: false,
  installPreReleaseVersion: false,
  pinned: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' }
}
[16:25:20] Extension signature verification result for christian-kohler.npm-intellisense: Success. Internal Code: 0. Executed: true. Duration: 828ms.
[16:25:20] Extracted extension to file:///home/<USER>/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5: christian-kohler.npm-intellisense
[16:25:20] Renamed to /home/<USER>/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5
[16:25:20] Extension installed successfully: christian-kohler.npm-intellisense file:///home/<USER>/.vscode-server/extensions/extensions.json
[16:25:31] Getting Manifest... augment.vscode-augment
[16:25:31] Installing extension: augment.vscode-augment {
  installPreReleaseVersion: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' }
}
[16:25:32] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 755ms.
[16:25:33] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.467.1: augment.vscode-augment
[16:25:33] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.467.1
[16:25:33] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
New EH opened, aborting shutdown
[16:29:33] New EH opened, aborting shutdown
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[17:29:47] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[17:29:47] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[17:33:31] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[17:33:31] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5000
}
