*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[17:45:21] 




[17:45:22] Extension host agent started.
[17:45:22] Marked extension as removed github.copilot-1.326.0
[17:45:22] [<unknown>][5a43c97b][ManagementConnection] Unknown reconnection token (never seen).
[17:45:22] Deleted marked for removal extension from disk github.copilot /home/<USER>/.vscode-server/extensions/github.copilot-1.326.0
[17:45:28] [<unknown>][24efdad0][ManagementConnection] New connection established.
[17:45:28] [<unknown>][c9006d69][ExtensionHostConnection] New connection established.
[17:45:28] [<unknown>][c9006d69][ExtensionHostConnection] <3393> Launched Extension Host Process.
[17:45:29] ComputeTargetPlatform: linux-x64
[17:45:33] ComputeTargetPlatform: linux-x64
[17:45:34] Getting Manifest... augment.vscode-augment
[17:45:34] Installing extension: augment.vscode-augment {
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[17:45:37] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 1834ms.
[17:45:38] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.470.1: augment.vscode-augment
[17:45:38] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.470.1
[17:45:38] Marked extension as removed augment.vscode-augment-0.467.1
[17:45:38] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
