2025-06-02 16:24:33.179 [info] 




2025-06-02 16:24:33.190 [info] Extension host agent started.
2025-06-02 16:24:33.225 [info] Started initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
2025-06-02 16:24:33.264 [info] ComputeTargetPlatform: linux-x64
2025-06-02 16:24:33.268 [info] Completed initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
2025-06-02 16:24:33.494 [info] [<unknown>][8c2576f7][ExtensionHostConnection] New connection established.
2025-06-02 16:24:33.495 [info] [<unknown>][5a43c97b][ManagementConnection] New connection established.
2025-06-02 16:24:33.575 [info] [<unknown>][8c2576f7][ExtensionHostConnection] <3319> Launched Extension Host Process.
2025-06-02 16:24:35.163 [info] ComputeTargetPlatform: linux-x64
2025-06-02 16:24:35.661 [info] Getting Manifest... github.vscode-pull-request-github
2025-06-02 16:24:35.752 [info] Installing extension: github.vscode-pull-request-github {"installPreReleaseVersion":false,"donotIncludePackAndDependencies":true,"donotVerifySignature":false,"context":{"clientTargetPlatform":"win32-x64"},"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.100.2","date":"2025-05-14T21:47:40.416Z"}}
2025-06-02 16:24:35.754 [info] Installing the extension without checking dependencies and pack github.vscode-pull-request-github
2025-06-02 16:24:37.974 [info] Extension signature verification result for github.vscode-pull-request-github: Success. Internal Code: 0. Executed: true. Duration: 1713ms.
2025-06-02 16:24:38.137 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0: github.vscode-pull-request-github
2025-06-02 16:24:38.160 [info] Renamed to /home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0
2025-06-02 16:24:38.194 [info] Extension installed successfully: github.vscode-pull-request-github file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-02 16:24:38.418 [info] Downloaded extension to file:///home/<USER>/.vscode-server/data/CachedExtensionVSIXs/120a1599-4772-4e96-a642-e0261511475d
2025-06-02 16:24:38.441 [info] Installing extension: github.copilot-chat {"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.100.2","date":"2025-05-14T21:47:40.416Z"}}
2025-06-02 16:24:38.791 [info] Getting Manifest... github.copilot
2025-06-02 16:24:38.831 [info] Installing extension: github.copilot {"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.100.2","date":"2025-05-14T21:47:40.416Z"},"pinned":false,"installGivenVersion":false,"context":{"dependecyOrPackExtensionInstall":true}}
2025-06-02 16:24:39.327 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2: github.copilot-chat
2025-06-02 16:24:39.340 [info] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2
2025-06-02 16:24:40.450 [info] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 1129ms.
2025-06-02 16:24:41.504 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.326.0: github.copilot
2025-06-02 16:24:41.537 [info] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.326.0
2025-06-02 16:24:41.570 [info] Extension installed successfully: github.copilot-chat file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-02 16:24:41.570 [info] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-02 16:24:46.492 [info] Downloaded extension to file:///home/<USER>/.vscode-server/data/CachedExtensionVSIXs/534cec37-1ae5-44f8-b1d9-c49d363d57bd
2025-06-02 16:24:46.500 [info] Installing extension: github.copilot {"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.100.2","date":"2025-05-14T21:47:40.416Z"}}
2025-06-02 16:24:46.755 [info] Getting Manifest... github.copilot-chat
2025-06-02 16:24:47.484 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.326.1596: github.copilot
2025-06-02 16:24:47.529 [info] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.326.1596
2025-06-02 16:24:47.539 [info] Marked extension as removed github.copilot-1.326.0
2025-06-02 16:24:47.552 [info] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-02 16:25:19.126 [info] Getting Manifest... christian-kohler.npm-intellisense
2025-06-02 16:25:19.205 [info] Installing extension: christian-kohler.npm-intellisense {"isMachineScoped":false,"installPreReleaseVersion":false,"pinned":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"win32-x64"},"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.100.2","date":"2025-05-14T21:47:40.416Z"}}
2025-06-02 16:25:20.533 [info] Extension signature verification result for christian-kohler.npm-intellisense: Success. Internal Code: 0. Executed: true. Duration: 828ms.
2025-06-02 16:25:20.594 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5: christian-kohler.npm-intellisense
2025-06-02 16:25:20.610 [info] Renamed to /home/<USER>/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5
2025-06-02 16:25:20.631 [info] Extension installed successfully: christian-kohler.npm-intellisense file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-02 16:25:31.342 [info] Getting Manifest... augment.vscode-augment
2025-06-02 16:25:31.437 [info] Installing extension: augment.vscode-augment {"installPreReleaseVersion":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"win32-x64"},"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.100.2","date":"2025-05-14T21:47:40.416Z"}}
2025-06-02 16:25:32.712 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 755ms.
2025-06-02 16:25:33.434 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.467.1: augment.vscode-augment
2025-06-02 16:25:33.476 [info] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.467.1
2025-06-02 16:25:33.516 [info] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-02 16:29:33.191 [info] New EH opened, aborting shutdown
