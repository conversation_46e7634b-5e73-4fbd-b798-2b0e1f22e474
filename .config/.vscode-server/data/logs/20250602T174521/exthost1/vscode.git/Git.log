2025-06-02 17:45:36.419 [info] [main] Log level: Info
2025-06-02 17:45:36.420 [info] [main] Validating found git in: "git"
2025-06-02 17:45:36.420 [info] [main] Using git "2.47.2" from "git"
2025-06-02 17:45:36.420 [info] [Model][doInitialScan] Initial repository scan started
2025-06-02 17:45:36.420 [info] > git rev-parse --show-toplevel [141ms]
2025-06-02 17:45:36.420 [info] > git rev-parse --git-dir --git-common-dir [1489ms]
2025-06-02 17:45:36.420 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-02 17:45:36.420 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-02 17:45:36.420 [info] > git rev-parse --show-toplevel [530ms]
2025-06-02 17:45:36.420 [info] > git config --get commit.template [546ms]
2025-06-02 17:45:36.420 [info] > git fetch [616ms]
2025-06-02 17:45:36.420 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-02 17:45:36.420 [info] > git check-ignore -v -z --stdin [124ms]
2025-06-02 17:45:36.420 [info] > git config --get commit.template [103ms]
2025-06-02 17:45:36.420 [info] > git rev-parse --show-toplevel [112ms]
2025-06-02 17:45:36.420 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [713ms]
2025-06-02 17:45:36.420 [info] > git rev-parse --show-toplevel [364ms]
2025-06-02 17:45:36.420 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [311ms]
2025-06-02 17:45:36.520 [info] > git rev-parse --show-toplevel [246ms]
2025-06-02 17:45:36.560 [info] > git rev-parse --show-toplevel [32ms]
2025-06-02 17:45:36.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [63ms]
2025-06-02 17:45:37.741 [info] > git config --get --local branch.main.vscode-merge-base [1115ms]
2025-06-02 17:45:37.753 [info] > git rev-parse --show-toplevel [1141ms]
2025-06-02 17:45:37.753 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1168ms]
2025-06-02 17:45:37.763 [info] > git config --get commit.template [1196ms]
2025-06-02 17:45:37.817 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [65ms]
2025-06-02 17:45:37.836 [info] > git merge-base refs/heads/main refs/remotes/origin/main [14ms]
2025-06-02 17:45:37.864 [info] > git rev-parse --show-toplevel [87ms]
2025-06-02 17:45:37.864 [info] > git config --get --local branch.main.vscode-merge-base [101ms]
2025-06-02 17:45:38.239 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [395ms]
2025-06-02 17:45:38.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [408ms]
2025-06-02 17:45:38.260 [info] > git rev-parse --show-toplevel [384ms]
2025-06-02 17:45:38.260 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [391ms]
2025-06-02 17:45:38.324 [info] > git status -z -uall [22ms]
2025-06-02 17:45:38.325 [info] > git rev-parse --show-toplevel [50ms]
2025-06-02 17:45:38.351 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [42ms]
2025-06-02 17:45:38.527 [info] > git rev-parse --show-toplevel [188ms]
2025-06-02 17:45:39.215 [info] > git rev-parse --show-toplevel [520ms]
2025-06-02 17:45:39.466 [info] > git rev-parse --show-toplevel [225ms]
2025-06-02 17:45:39.576 [info] > git rev-parse --show-toplevel [95ms]
2025-06-02 17:45:40.207 [info] > git rev-parse --show-toplevel [620ms]
2025-06-02 17:45:40.390 [info] > git rev-parse --show-toplevel [13ms]
2025-06-02 17:45:40.392 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-02 17:45:40.650 [info] > git show --textconv :client/src/components/ai/AIConfigurationSection.tsx [245ms]
2025-06-02 17:45:40.651 [info] > git ls-files --stage -- client/src/components/ai/AIConfigurationSection.tsx [227ms]
2025-06-02 17:45:40.746 [info] > git cat-file -s 9866f6070f83511f5fc1ade6ff77310ecb057367 [63ms]
2025-06-02 17:45:40.747 [info] > git config --get commit.template [208ms]
2025-06-02 17:45:40.799 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [37ms]
2025-06-02 17:45:40.869 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [18ms]
2025-06-02 17:45:40.872 [info] > git status -z -uall [31ms]
2025-06-02 17:45:41.334 [info] > git config --get --local branch.main.github-pr-owner-number [241ms]
2025-06-02 17:45:41.334 [warning] [Git][config] git config failed: Failed to execute git
2025-06-02 17:45:43.084 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- client/src/components/ai/AIConfigurationSection.tsx [1757ms]
2025-06-02 17:45:48.476 [info] > git config --get commit.template [25ms]
2025-06-02 17:45:48.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 17:45:48.497 [info] > git status -z -uall [5ms]
2025-06-02 17:45:48.498 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:45:53.516 [info] > git config --get commit.template [6ms]
2025-06-02 17:45:53.517 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:45:53.529 [info] > git status -z -uall [6ms]
2025-06-02 17:45:53.530 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:45:58.566 [info] > git config --get commit.template [1ms]
2025-06-02 17:45:58.591 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 17:45:58.662 [info] > git status -z -uall [48ms]
2025-06-02 17:45:58.663 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:46:03.706 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:46:03.706 [info] > git config --get commit.template [21ms]
2025-06-02 17:46:03.729 [info] > git status -z -uall [13ms]
2025-06-02 17:46:03.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:46:09.728 [info] > git config --get commit.template [5ms]
2025-06-02 17:46:09.729 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:46:09.741 [info] > git status -z -uall [8ms]
2025-06-02 17:46:09.743 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:46:14.767 [info] > git config --get commit.template [11ms]
2025-06-02 17:46:14.768 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:46:14.782 [info] > git status -z -uall [7ms]
2025-06-02 17:46:14.784 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:46:19.801 [info] > git config --get commit.template [6ms]
2025-06-02 17:46:19.802 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:46:19.814 [info] > git status -z -uall [6ms]
2025-06-02 17:46:19.819 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 17:46:24.833 [info] > git config --get commit.template [6ms]
2025-06-02 17:46:24.834 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:46:24.848 [info] > git status -z -uall [7ms]
2025-06-02 17:46:24.850 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:46:26.715 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-02 17:46:28.877 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-02 17:46:29.892 [info] > git config --get commit.template [5ms]
2025-06-02 17:46:29.896 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 17:46:29.906 [info] > git status -z -uall [5ms]
2025-06-02 17:46:29.908 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:46:34.921 [info] > git config --get commit.template [4ms]
2025-06-02 17:46:34.923 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:46:34.935 [info] > git status -z -uall [7ms]
2025-06-02 17:46:34.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:46:39.960 [info] > git config --get commit.template [12ms]
2025-06-02 17:46:39.972 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 17:46:39.990 [info] > git status -z -uall [9ms]
2025-06-02 17:46:39.993 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:46:45.007 [info] > git config --get commit.template [3ms]
2025-06-02 17:46:45.015 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:46:45.030 [info] > git status -z -uall [7ms]
2025-06-02 17:46:45.033 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 17:46:58.599 [info] > git config --get commit.template [8ms]
2025-06-02 17:46:58.600 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:46:58.614 [info] > git status -z -uall [9ms]
2025-06-02 17:46:58.615 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:47:03.628 [info] > git config --get commit.template [4ms]
2025-06-02 17:47:03.629 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:47:03.638 [info] > git status -z -uall [5ms]
2025-06-02 17:47:03.639 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:47:08.687 [info] > git config --get commit.template [2ms]
2025-06-02 17:47:08.698 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 17:47:08.723 [info] > git status -z -uall [8ms]
2025-06-02 17:47:08.725 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:47:13.744 [info] > git config --get commit.template [7ms]
2025-06-02 17:47:13.745 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:47:13.761 [info] > git status -z -uall [8ms]
2025-06-02 17:47:13.762 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:47:18.779 [info] > git config --get commit.template [5ms]
2025-06-02 17:47:18.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:47:18.797 [info] > git status -z -uall [10ms]
2025-06-02 17:47:18.798 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:47:23.809 [info] > git config --get commit.template [1ms]
2025-06-02 17:47:23.821 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:47:23.836 [info] > git status -z -uall [8ms]
2025-06-02 17:47:23.838 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:47:28.849 [info] > git config --get commit.template [4ms]
2025-06-02 17:47:28.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:47:28.859 [info] > git status -z -uall [4ms]
2025-06-02 17:47:28.860 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:47:41.149 [info] > git config --get commit.template [11ms]
2025-06-02 17:47:41.151 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 17:47:41.167 [info] > git status -z -uall [8ms]
2025-06-02 17:47:41.169 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:47:46.182 [info] > git config --get commit.template [4ms]
2025-06-02 17:47:46.183 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:47:46.195 [info] > git status -z -uall [8ms]
2025-06-02 17:47:46.195 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:47:51.214 [info] > git config --get commit.template [7ms]
2025-06-02 17:47:51.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:47:51.229 [info] > git status -z -uall [5ms]
2025-06-02 17:47:51.231 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:47:56.249 [info] > git config --get commit.template [7ms]
2025-06-02 17:47:56.250 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:47:56.262 [info] > git status -z -uall [5ms]
2025-06-02 17:47:56.264 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:48:01.283 [info] > git config --get commit.template [9ms]
2025-06-02 17:48:01.283 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:48:01.309 [info] > git status -z -uall [14ms]
2025-06-02 17:48:01.310 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:48:06.325 [info] > git config --get commit.template [4ms]
2025-06-02 17:48:06.326 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:48:06.336 [info] > git status -z -uall [5ms]
2025-06-02 17:48:06.337 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:48:11.356 [info] > git config --get commit.template [5ms]
2025-06-02 17:48:11.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:48:11.371 [info] > git status -z -uall [6ms]
2025-06-02 17:48:11.372 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:48:16.384 [info] > git config --get commit.template [4ms]
2025-06-02 17:48:16.385 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:48:16.394 [info] > git status -z -uall [4ms]
2025-06-02 17:48:16.395 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:48:21.411 [info] > git config --get commit.template [5ms]
2025-06-02 17:48:21.412 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:48:21.422 [info] > git status -z -uall [4ms]
2025-06-02 17:48:21.424 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:48:26.438 [info] > git config --get commit.template [2ms]
2025-06-02 17:48:26.463 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-02 17:48:26.482 [info] > git status -z -uall [7ms]
2025-06-02 17:48:26.483 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:48:31.500 [info] > git config --get commit.template [5ms]
2025-06-02 17:48:31.501 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:48:31.514 [info] > git status -z -uall [6ms]
2025-06-02 17:48:31.515 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:48:38.146 [info] > git config --get commit.template [5ms]
2025-06-02 17:48:38.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:48:38.159 [info] > git status -z -uall [6ms]
2025-06-02 17:48:38.160 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:48:43.198 [info] > git config --get commit.template [19ms]
2025-06-02 17:48:43.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:48:43.218 [info] > git status -z -uall [9ms]
2025-06-02 17:48:43.220 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:48:50.725 [info] > git config --get commit.template [7ms]
2025-06-02 17:48:50.726 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:48:50.740 [info] > git status -z -uall [6ms]
2025-06-02 17:48:50.742 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:48:55.767 [info] > git config --get commit.template [7ms]
2025-06-02 17:48:55.768 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:48:55.780 [info] > git status -z -uall [5ms]
2025-06-02 17:48:55.781 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:49:13.182 [info] > git config --get commit.template [6ms]
2025-06-02 17:49:13.183 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:49:13.197 [info] > git status -z -uall [7ms]
2025-06-02 17:49:13.199 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:19.188 [info] > git config --get commit.template [6ms]
2025-06-02 17:49:19.190 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:49:19.202 [info] > git status -z -uall [6ms]
2025-06-02 17:49:19.203 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:49:24.223 [info] > git config --get commit.template [1ms]
2025-06-02 17:49:24.231 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:49:24.243 [info] > git status -z -uall [6ms]
2025-06-02 17:49:24.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:29.264 [info] > git config --get commit.template [9ms]
2025-06-02 17:49:29.265 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:49:29.277 [info] > git status -z -uall [5ms]
2025-06-02 17:49:29.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:34.297 [info] > git config --get commit.template [6ms]
2025-06-02 17:49:34.298 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:49:34.310 [info] > git status -z -uall [6ms]
2025-06-02 17:49:34.311 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:39.330 [info] > git config --get commit.template [8ms]
2025-06-02 17:49:39.331 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:49:39.350 [info] > git status -z -uall [10ms]
2025-06-02 17:49:39.351 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:44.366 [info] > git config --get commit.template [5ms]
2025-06-02 17:49:44.367 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:49:44.379 [info] > git status -z -uall [6ms]
2025-06-02 17:49:44.381 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:49.415 [info] > git config --get commit.template [11ms]
2025-06-02 17:49:49.416 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:49:49.428 [info] > git status -z -uall [6ms]
2025-06-02 17:49:49.430 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:54.441 [info] > git config --get commit.template [2ms]
2025-06-02 17:49:54.449 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:49:54.459 [info] > git status -z -uall [5ms]
2025-06-02 17:49:54.460 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:59.475 [info] > git config --get commit.template [0ms]
2025-06-02 17:49:59.489 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:49:59.506 [info] > git status -z -uall [7ms]
2025-06-02 17:49:59.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:50:04.527 [info] > git config --get commit.template [8ms]
2025-06-02 17:50:04.528 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:50:04.541 [info] > git status -z -uall [6ms]
2025-06-02 17:50:04.542 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:50:09.562 [info] > git config --get commit.template [7ms]
2025-06-02 17:50:09.563 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:50:09.580 [info] > git status -z -uall [8ms]
2025-06-02 17:50:09.586 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 17:50:14.615 [info] > git config --get commit.template [14ms]
2025-06-02 17:50:14.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:50:14.723 [info] > git status -z -uall [93ms]
2025-06-02 17:50:14.725 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [79ms]
2025-06-02 17:50:19.749 [info] > git config --get commit.template [11ms]
2025-06-02 17:50:19.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-02 17:50:19.780 [info] > git status -z -uall [14ms]
2025-06-02 17:50:19.783 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:50:24.809 [info] > git config --get commit.template [11ms]
2025-06-02 17:50:24.810 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:50:24.828 [info] > git status -z -uall [9ms]
2025-06-02 17:50:24.831 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:50:29.864 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:50:29.865 [info] > git config --get commit.template [17ms]
2025-06-02 17:50:29.898 [info] > git status -z -uall [18ms]
2025-06-02 17:50:29.900 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:50:34.950 [info] > git config --get commit.template [21ms]
2025-06-02 17:50:34.955 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-02 17:50:34.983 [info] > git status -z -uall [13ms]
2025-06-02 17:50:34.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 17:50:40.028 [info] > git config --get commit.template [28ms]
2025-06-02 17:50:40.031 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-02 17:50:40.060 [info] > git status -z -uall [12ms]
2025-06-02 17:50:40.063 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:50:45.081 [info] > git config --get commit.template [1ms]
2025-06-02 17:50:45.110 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:50:45.140 [info] > git status -z -uall [13ms]
2025-06-02 17:50:45.143 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:50:50.174 [info] > git config --get commit.template [13ms]
2025-06-02 17:50:50.176 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:50:50.203 [info] > git status -z -uall [15ms]
2025-06-02 17:50:50.204 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:50:55.237 [info] > git config --get commit.template [11ms]
2025-06-02 17:50:55.238 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:50:55.265 [info] > git status -z -uall [14ms]
2025-06-02 17:50:55.266 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:51:00.301 [info] > git config --get commit.template [9ms]
2025-06-02 17:51:00.302 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:51:00.334 [info] > git status -z -uall [12ms]
2025-06-02 17:51:00.336 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:51:05.360 [info] > git config --get commit.template [9ms]
2025-06-02 17:51:05.361 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:51:05.374 [info] > git status -z -uall [5ms]
2025-06-02 17:51:05.378 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 17:51:10.400 [info] > git config --get commit.template [10ms]
2025-06-02 17:51:10.400 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:51:10.419 [info] > git status -z -uall [9ms]
2025-06-02 17:51:10.423 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:51:15.442 [info] > git config --get commit.template [7ms]
2025-06-02 17:51:15.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:51:15.471 [info] > git status -z -uall [18ms]
2025-06-02 17:51:15.476 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 17:51:20.494 [info] > git config --get commit.template [3ms]
2025-06-02 17:51:20.508 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 17:51:20.525 [info] > git status -z -uall [9ms]
2025-06-02 17:51:20.526 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:51:25.561 [info] > git config --get commit.template [13ms]
2025-06-02 17:51:25.564 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 17:51:25.592 [info] > git status -z -uall [18ms]
2025-06-02 17:51:25.599 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-02 17:51:30.631 [info] > git config --get commit.template [10ms]
2025-06-02 17:51:30.632 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:51:30.652 [info] > git status -z -uall [11ms]
2025-06-02 17:51:30.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:51:39.884 [info] > git config --get commit.template [3ms]
2025-06-02 17:51:39.900 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-02 17:51:39.923 [info] > git status -z -uall [9ms]
2025-06-02 17:51:39.925 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:51:44.955 [info] > git config --get commit.template [11ms]
2025-06-02 17:51:44.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:51:44.974 [info] > git status -z -uall [9ms]
2025-06-02 17:51:44.976 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:51:50.001 [info] > git config --get commit.template [9ms]
2025-06-02 17:51:50.035 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 17:51:50.079 [info] > git status -z -uall [23ms]
2025-06-02 17:51:50.079 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:51:55.106 [info] > git config --get commit.template [15ms]
2025-06-02 17:51:55.106 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:51:55.125 [info] > git status -z -uall [8ms]
2025-06-02 17:51:55.127 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:52:31.740 [info] > git config --get commit.template [7ms]
2025-06-02 17:52:31.741 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:52:31.759 [info] > git status -z -uall [10ms]
2025-06-02 17:52:31.762 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:53:32.678 [info] > git config --get commit.template [9ms]
2025-06-02 17:53:32.679 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:53:32.696 [info] > git status -z -uall [10ms]
2025-06-02 17:53:32.696 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:53:37.712 [info] > git config --get commit.template [2ms]
2025-06-02 17:53:37.723 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:53:37.754 [info] > git status -z -uall [17ms]
2025-06-02 17:53:37.756 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:53:42.778 [info] > git config --get commit.template [1ms]
2025-06-02 17:53:42.795 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:53:42.826 [info] > git status -z -uall [17ms]
2025-06-02 17:53:42.826 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:53:47.852 [info] > git config --get commit.template [10ms]
2025-06-02 17:53:47.854 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 17:53:47.873 [info] > git status -z -uall [11ms]
2025-06-02 17:53:47.876 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 17:53:52.896 [info] > git config --get commit.template [9ms]
2025-06-02 17:53:52.897 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:53:52.915 [info] > git status -z -uall [8ms]
2025-06-02 17:53:52.918 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:53:57.934 [info] > git config --get commit.template [1ms]
2025-06-02 17:53:57.943 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:53:57.963 [info] > git status -z -uall [11ms]
2025-06-02 17:53:57.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:54:03.002 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:54:03.014 [info] > git config --get commit.template [31ms]
2025-06-02 17:54:03.045 [info] > git status -z -uall [17ms]
2025-06-02 17:54:03.048 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:54:08.082 [info] > git config --get commit.template [15ms]
2025-06-02 17:54:08.095 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:54:08.117 [info] > git status -z -uall [9ms]
2025-06-02 17:54:08.120 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:54:13.134 [info] > git config --get commit.template [0ms]
2025-06-02 17:54:13.151 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:54:13.178 [info] > git status -z -uall [17ms]
2025-06-02 17:54:13.178 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 17:54:18.211 [info] > git config --get commit.template [16ms]
2025-06-02 17:54:18.211 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:54:18.241 [info] > git status -z -uall [14ms]
2025-06-02 17:54:18.247 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 17:54:23.281 [info] > git config --get commit.template [23ms]
2025-06-02 17:54:23.281 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-02 17:54:23.301 [info] > git status -z -uall [7ms]
2025-06-02 17:54:23.304 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:54:28.336 [info] > git config --get commit.template [11ms]
2025-06-02 17:54:28.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-02 17:54:28.377 [info] > git status -z -uall [11ms]
2025-06-02 17:54:28.378 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:54:34.486 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:54:34.486 [info] > git config --get commit.template [17ms]
2025-06-02 17:54:34.508 [info] > git status -z -uall [12ms]
2025-06-02 17:54:34.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:54:39.542 [info] > git config --get commit.template [15ms]
2025-06-02 17:54:39.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:54:39.569 [info] > git status -z -uall [10ms]
2025-06-02 17:54:39.571 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:54:44.588 [info] > git config --get commit.template [1ms]
2025-06-02 17:54:44.602 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-02 17:54:44.620 [info] > git status -z -uall [8ms]
2025-06-02 17:54:44.622 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:54:49.640 [info] > git config --get commit.template [2ms]
2025-06-02 17:54:49.655 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:54:49.683 [info] > git status -z -uall [9ms]
2025-06-02 17:54:49.745 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [62ms]
2025-06-02 17:54:54.761 [info] > git config --get commit.template [0ms]
2025-06-02 17:54:54.772 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:54:54.790 [info] > git status -z -uall [8ms]
2025-06-02 17:54:54.792 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:54:59.882 [info] > git config --get commit.template [73ms]
2025-06-02 17:54:59.895 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:54:59.920 [info] > git status -z -uall [16ms]
2025-06-02 17:54:59.921 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 17:55:04.945 [info] > git config --get commit.template [9ms]
2025-06-02 17:55:04.946 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:55:04.960 [info] > git status -z -uall [7ms]
2025-06-02 17:55:04.962 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:55:09.980 [info] > git config --get commit.template [4ms]
2025-06-02 17:55:09.997 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-02 17:55:10.022 [info] > git status -z -uall [9ms]
2025-06-02 17:55:10.025 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:55:15.052 [info] > git config --get commit.template [10ms]
2025-06-02 17:55:15.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 17:55:15.079 [info] > git status -z -uall [13ms]
2025-06-02 17:55:15.081 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:55:21.632 [info] > git config --get commit.template [2ms]
2025-06-02 17:55:21.647 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:55:21.674 [info] > git status -z -uall [14ms]
2025-06-02 17:55:21.675 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:55:26.693 [info] > git config --get commit.template [1ms]
2025-06-02 17:55:26.708 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:55:26.733 [info] > git status -z -uall [11ms]
2025-06-02 17:55:26.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:55:31.757 [info] > git config --get commit.template [8ms]
2025-06-02 17:55:31.760 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 17:55:31.779 [info] > git status -z -uall [9ms]
2025-06-02 17:55:31.784 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 17:55:36.809 [info] > git config --get commit.template [8ms]
2025-06-02 17:55:36.811 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:55:36.828 [info] > git status -z -uall [9ms]
2025-06-02 17:55:36.831 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:55:41.898 [info] > git config --get commit.template [19ms]
2025-06-02 17:55:41.898 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 17:55:41.915 [info] > git status -z -uall [7ms]
2025-06-02 17:55:41.918 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:55:47.070 [info] > git config --get commit.template [26ms]
2025-06-02 17:55:47.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-02 17:55:47.149 [info] > git status -z -uall [40ms]
2025-06-02 17:55:47.149 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:55:52.168 [info] > git config --get commit.template [2ms]
2025-06-02 17:55:52.180 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:55:52.210 [info] > git status -z -uall [11ms]
2025-06-02 17:55:52.212 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:55:57.233 [info] > git config --get commit.template [2ms]
2025-06-02 17:55:57.247 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-02 17:55:57.279 [info] > git status -z -uall [21ms]
2025-06-02 17:55:57.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:56:02.299 [info] > git config --get commit.template [2ms]
2025-06-02 17:56:02.332 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-02 17:56:02.369 [info] > git status -z -uall [17ms]
2025-06-02 17:56:02.372 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:56:07.389 [info] > git config --get commit.template [1ms]
2025-06-02 17:56:07.406 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:56:07.456 [info] > git status -z -uall [16ms]
2025-06-02 17:56:07.459 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:56:12.576 [info] > git config --get commit.template [2ms]
2025-06-02 17:56:12.592 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:56:12.643 [info] > git status -z -uall [22ms]
2025-06-02 17:56:12.646 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:56:17.665 [info] > git config --get commit.template [1ms]
2025-06-02 17:56:17.676 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:56:17.695 [info] > git status -z -uall [7ms]
2025-06-02 17:56:17.698 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:56:22.749 [info] > git config --get commit.template [27ms]
2025-06-02 17:56:22.761 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-02 17:56:22.802 [info] > git status -z -uall [18ms]
2025-06-02 17:56:22.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-02 17:56:55.957 [info] > git config --get commit.template [12ms]
2025-06-02 17:56:55.958 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:56:55.981 [info] > git status -z -uall [9ms]
2025-06-02 17:56:55.984 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:57:01.159 [info] > git config --get commit.template [4ms]
2025-06-02 17:57:01.185 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-02 17:57:01.215 [info] > git status -z -uall [12ms]
2025-06-02 17:57:01.217 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:58:55.721 [info] > git config --get commit.template [10ms]
2025-06-02 17:58:55.724 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:58:55.754 [info] > git status -z -uall [11ms]
2025-06-02 17:58:55.754 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:00:17.317 [info] > git config --get commit.template [17ms]
2025-06-02 18:00:17.319 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:00:17.344 [info] > git status -z -uall [13ms]
2025-06-02 18:00:17.353 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-02 18:00:35.604 [info] > git config --get commit.template [10ms]
2025-06-02 18:00:35.605 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:00:35.625 [info] > git status -z -uall [12ms]
2025-06-02 18:00:35.628 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:00:44.617 [info] > git config --get commit.template [1ms]
2025-06-02 18:00:44.628 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:00:44.650 [info] > git status -z -uall [9ms]
2025-06-02 18:00:44.651 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:00:49.679 [info] > git config --get commit.template [10ms]
2025-06-02 18:00:49.680 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:00:49.704 [info] > git status -z -uall [11ms]
2025-06-02 18:00:49.708 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 18:00:54.723 [info] > git config --get commit.template [1ms]
2025-06-02 18:00:54.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:00:54.751 [info] > git status -z -uall [6ms]
2025-06-02 18:00:54.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:00:59.780 [info] > git config --get commit.template [11ms]
2025-06-02 18:00:59.781 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:00:59.798 [info] > git status -z -uall [9ms]
2025-06-02 18:00:59.799 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:01:04.819 [info] > git config --get commit.template [7ms]
2025-06-02 18:01:04.821 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:01:04.843 [info] > git status -z -uall [10ms]
2025-06-02 18:01:04.844 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:01:09.864 [info] > git config --get commit.template [2ms]
2025-06-02 18:01:09.877 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:01:09.893 [info] > git status -z -uall [7ms]
2025-06-02 18:01:09.895 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:01:14.923 [info] > git config --get commit.template [12ms]
2025-06-02 18:01:14.923 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:01:14.942 [info] > git status -z -uall [9ms]
2025-06-02 18:01:14.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:01:19.956 [info] > git config --get commit.template [1ms]
2025-06-02 18:01:19.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:01:19.990 [info] > git status -z -uall [12ms]
2025-06-02 18:01:19.990 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:01:25.015 [info] > git config --get commit.template [10ms]
2025-06-02 18:01:25.016 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:01:25.050 [info] > git status -z -uall [13ms]
2025-06-02 18:01:25.052 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:01:30.077 [info] > git config --get commit.template [11ms]
2025-06-02 18:01:30.079 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:01:30.095 [info] > git status -z -uall [8ms]
2025-06-02 18:01:30.100 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 18:01:35.111 [info] > git config --get commit.template [0ms]
2025-06-02 18:01:35.121 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:01:35.138 [info] > git status -z -uall [11ms]
2025-06-02 18:01:35.138 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:01:40.158 [info] > git config --get commit.template [7ms]
2025-06-02 18:01:40.160 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:01:40.181 [info] > git status -z -uall [8ms]
2025-06-02 18:01:40.184 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:01:45.206 [info] > git config --get commit.template [9ms]
2025-06-02 18:01:45.208 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:01:45.224 [info] > git status -z -uall [8ms]
2025-06-02 18:01:45.226 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:01:50.245 [info] > git config --get commit.template [9ms]
2025-06-02 18:01:50.246 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:01:50.270 [info] > git status -z -uall [10ms]
2025-06-02 18:01:50.273 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:01:55.294 [info] > git config --get commit.template [1ms]
2025-06-02 18:01:55.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-02 18:01:55.352 [info] > git status -z -uall [14ms]
2025-06-02 18:01:55.355 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:02:00.375 [info] > git config --get commit.template [1ms]
2025-06-02 18:02:00.390 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:02:00.406 [info] > git status -z -uall [8ms]
2025-06-02 18:02:00.407 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:02:06.629 [info] > git config --get commit.template [2ms]
2025-06-02 18:02:06.639 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:02:06.655 [info] > git status -z -uall [7ms]
2025-06-02 18:02:06.657 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:02:11.681 [info] > git config --get commit.template [9ms]
2025-06-02 18:02:11.686 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 18:02:11.709 [info] > git status -z -uall [10ms]
2025-06-02 18:02:11.713 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:02:16.737 [info] > git config --get commit.template [9ms]
2025-06-02 18:02:16.738 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:02:16.756 [info] > git status -z -uall [9ms]
2025-06-02 18:02:16.758 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:02:21.775 [info] > git config --get commit.template [0ms]
2025-06-02 18:02:21.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:02:21.813 [info] > git status -z -uall [13ms]
2025-06-02 18:02:21.816 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:02:26.842 [info] > git config --get commit.template [10ms]
2025-06-02 18:02:26.844 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:02:26.882 [info] > git status -z -uall [16ms]
2025-06-02 18:02:26.884 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:02:31.910 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:02:31.910 [info] > git config --get commit.template [9ms]
2025-06-02 18:02:31.932 [info] > git status -z -uall [10ms]
2025-06-02 18:02:31.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-02 18:02:36.957 [info] > git config --get commit.template [0ms]
2025-06-02 18:02:36.971 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:02:36.996 [info] > git status -z -uall [15ms]
2025-06-02 18:02:36.999 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:02:42.018 [info] > git config --get commit.template [1ms]
2025-06-02 18:02:42.100 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [69ms]
2025-06-02 18:02:42.129 [info] > git status -z -uall [13ms]
2025-06-02 18:02:42.130 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:02:47.157 [info] > git config --get commit.template [11ms]
2025-06-02 18:02:47.161 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:02:47.190 [info] > git status -z -uall [15ms]
2025-06-02 18:02:47.192 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:02:52.210 [info] > git config --get commit.template [2ms]
2025-06-02 18:02:52.219 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:02:52.240 [info] > git status -z -uall [13ms]
2025-06-02 18:02:52.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 18:02:57.274 [info] > git config --get commit.template [11ms]
2025-06-02 18:02:57.275 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:02:57.300 [info] > git status -z -uall [15ms]
2025-06-02 18:02:57.305 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 18:03:02.326 [info] > git config --get commit.template [1ms]
2025-06-02 18:03:02.352 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-02 18:03:02.401 [info] > git status -z -uall [29ms]
2025-06-02 18:03:02.402 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:03:07.424 [info] > git config --get commit.template [0ms]
2025-06-02 18:03:07.436 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-02 18:03:07.456 [info] > git status -z -uall [12ms]
2025-06-02 18:03:07.459 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:03:12.485 [info] > git config --get commit.template [2ms]
2025-06-02 18:03:12.501 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:03:12.526 [info] > git status -z -uall [12ms]
2025-06-02 18:03:12.528 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
