# Development & Documentation
- Always maintain and update markdown files in /docs/ folder with accurate, specific information about the codebase, keeping documentation current with any changes made.

# Deployment
- ChewyAI production setup completed with security-first architecture, optimized builds, proper environment variable handling, comprehensive documentation, and successful Replit deployment configuration.