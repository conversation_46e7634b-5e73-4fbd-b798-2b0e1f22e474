[["5fc35e27-0f79-4743-95b1-a4dd3630ae99", {"value": {"selectedCode": "", "prefix": "# Specifies the Nix environment and Replit modules to use.\n", "suffix": "# 'nodejs-20' provides the Node.js runtime.\n# 'web' is necessary for Replit to serve your application on standard HTTP/HTTPS ports.\n# Removed 'postgresql-16' as your Drizzle and Supabase configurations point to an external Supabase DB.\nmodules = [\"nodejs-20\", \"web\"]\n\n# Command to execute when the \"Run\" button in the Replit IDE is pressed.\n# 'npm run dev' typically starts your development servers (frontend and backend).\nrun = \"npm run dev\"\n\n# Files and directories to hide from the Replit file explorer.\nhidden = [\".config\", \".git\", \"generated-icon.png\", \"node_modules\", \"dist\"]\n\n[nix]\n# Specifies the Nix channel for environment reproducibility.\nchannel = \"stable-24_05\"\n\n[deployment]\n# Configures the deployment target on Replit.\ndeploymentTarget = \"autoscale\"\nbuild = [\"sh\", \"-c\", \"npm run build\"]\nrun = [\"sh\", \"-c\", \"NODE_ENV=production PORT=80 node dist/index.js\"]\n\n# Health check endpoint for monitoring\nhealthcheck = \"/api/health\"\n\n# Environment variables for production deployment\n[deployment.env]\nNODE_ENV = \"production\"\nPORT = \"80\"\n\n# SPA Fallback: Rewrites all non-file paths to /index.html for client-side routing.\n# Your server/index.ts also handles this, but this Replit rule can act as a fallback.\n[[deployment.rewrites]]\nfrom = \"/*\"\nto = \"/index.html\"\n\n# CORS Headers:\n# Your application (server/index.ts) already configures CORS using `app.use(cors(...))`.\n# The application-level CORS is more specific and generally preferred.\n# These global Replit-level headers might be redundant or overly permissive.\n# Consider removing these if your application's CORS handling is sufficient.\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Origin\"\nvalue = \"*\"\n\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Methods\"\nvalue = \"GET, POST, PUT, DELETE, OPTIONS\"\n\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Headers\"\nvalue = \"Content-Type, Authorization\"\n\n[[ports]]\nlocalPort = 80\nexternalPort = 80\n\n[[ports]]\nlocalPort = 3000\nexternalPort = 3000\n\n[[ports]]\nlocalPort = 5000\nexternalPort = 5001\n\n[[ports]]\nlocalPort = 24678\nexternalPort = 24678\n\n# Workflow for the \"Run\" button in the Replit IDE.\n[workflows]\nrunButton = \"Project\"\n\n[[workflows.workflow]]\nname = \"Project\"\nmode = \"parallel\"\nauthor = \"agent\"\n[[workflows.workflow.tasks]]\ntask = \"workflow.run\"\nargs = \"Start application\"\n\n[[workflows.workflow]]\nname = \"Start application\"\nauthor = \"agent\"\n[[workflows.workflow.tasks]]\ntask = \"shell.exec\"\nargs = \"npm run dev\" # This executes your development script.\nwaitForPort = 3000    # Waits for the Vite frontend dev server to be ready on port 3000.\n", "path": ".replit", "language": "plaintext", "prefixBegin": 0, "suffixEnd": 0}}], ["3fddee2e-6ef6-4c81-b5e1-4cd4a52f0a2b", {"value": {"selectedCode": "", "prefix": "# Specifies the Nix environment and Replit modules to use.\n# 'nodejs-20' provides the Node.js runtime.\n# 'web' is necessary for Replit to serve your application on standard HTTP/HTTPS ports.\n# Removed 'postgresql-16' as your Drizzle and Supabase configurations point to an external Supabase DB.\nmodules = [\"nodejs-20\", \"web\"]\n\n# Command to execute when the \"Run\" button in the Replit IDE is pressed.\n# 'npm run dev' typically starts your development servers (frontend and backend).\nrun = \"npm run dev\"\n\n# Files and directories to hide from the Replit file explorer.\nhidden = [\".config\", \".git\", \"generated-icon.png\", \"node_modules\", \"dist\"]\n\n[nix]\n# Specifies the Nix channel for environment reproducibility.\nchannel = \"stable-24_05\"\n\n[deployment]\n# Configures the deployment target on Replit.\ndeploymentTarget = \"autoscale\"\nbuild = [\"sh\", \"-c\", \"npm run build\"]\nrun = [\"sh\", \"-c\", \"NODE_ENV=production PORT=80 node dist/index.js\"]\n\n# Health check endpoint for monitoring\nhealthcheck = \"/api/health\"\n\n# Environment variables for production deployment\n[deployment.env]\nNODE_ENV = \"production\"\nPORT = \"80\"\n\n# SPA Fallback: Rewrites all non-file paths to /index.html for client-side routing.\n# Your server/index.ts also handles this, but this Replit rule can act as a fallback.\n[[deployment.rewrites]]\nfrom = \"/*\"\nto = \"/index.html\"\n\n# CORS Headers:\n# Your application (server/index.ts) already configures CORS using `app.use(cors(...))`.\n# The application-level CORS is more specific and generally preferred.\n# These global Replit-level headers might be redundant or overly permissive.\n# Consider removing these if your application's CORS handling is sufficient.\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Origin\"\nvalue = \"*\"\n\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Methods\"\nvalue = \"GET, POST, PUT, DELETE, OPTIONS\"\n\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Headers\"\nvalue = \"Content-Type, Authorization\"\n\n[[ports]]\nlocalPort = 80\nexternalPort = 80\n\n[[ports]]\nlocalPort = 3000\nexternalPort = 3000\n\n[[ports]]\nlocalPort = 5000\nexternalPort = 5000\n", "suffix": "\n[[ports]]\nlocalPort = 24678\nexternalPort = 24678\n\n# Workflow for the \"Run\" button in the Replit IDE.\n[workflows]\nrunButton = \"Project\"\n\n[[workflows.workflow]]\nname = \"Project\"\nmode = \"parallel\"\nauthor = \"agent\"\n[[workflows.workflow.tasks]]\ntask = \"workflow.run\"\nargs = \"Start application\"\n\n[[workflows.workflow]]\nname = \"Start application\"\nauthor = \"agent\"\n[[workflows.workflow.tasks]]\ntask = \"shell.exec\"\nargs = \"npm run dev\" # This executes your development script.\nwaitForPort = 3000    # Waits for the Vite frontend dev server to be ready on port 3000.\n", "path": ".replit", "language": "plaintext", "prefixBegin": 0, "suffixEnd": 0}}], ["8fea6914-7745-4b79-b376-b86c9<PERSON>ca474", {"value": {"selectedCode": "", "prefix": "# Specifies the Nix environment and Replit modules to use.\n# 'nodejs-20' provides the Node.js runtime.\n# 'web' is necessary for Replit to serve your application on standard HTTP/HTTPS ports.\n# Removed 'postgresql-16' as your Drizzle and Supabase configurations point to an external Supabase DB.\nmodules = [\"nodejs-20\", \"web\"]\n\n# Command to execute when the \"Run\" button in the Replit IDE is pressed.\n# 'npm run dev' typically starts your development servers (frontend and backend).\nrun = \"npm run dev\"\n\n# Files and directories to hide from the Replit file explorer.\nhidden = [\".config\", \".git\", \"generated-icon.png\", \"node_modules\", \"dist\"]\n\n[nix]\n# Specifies the Nix channel for environment reproducibility.\nchannel = \"stable-24_05\"\n\n[deployment]\n# Configures the deployment target on Replit.\ndeploymentTarget = \"autoscale\"\nbuild = [\"sh\", \"-c\", \"npm run build\"]\n", "suffix": "run = [\"sh\", \"-c\", \"NODE_ENV=production PORT=80 tsx server/index.ts\"]\n\n# Health check endpoint for monitoring\nhealthcheck = \"/api/health\"\n\n# Environment variables for production deployment\n[deployment.env]\nNODE_ENV = \"production\"\nPORT = \"80\"\n\n# SPA Fallback: Rewrites all non-file paths to /index.html for client-side routing.\n# Your server/index.ts also handles this, but this Replit rule can act as a fallback.\n[[deployment.rewrites]]\nfrom = \"/*\"\nto = \"/index.html\"\n\n# CORS Headers:\n# Your application (server/index.ts) already configures CORS using `app.use(cors(...))`.\n# The application-level CORS is more specific and generally preferred.\n# These global Replit-level headers might be redundant or overly permissive.\n# Consider removing these if your application's CORS handling is sufficient.\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Origin\"\nvalue = \"*\"\n\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Methods\"\nvalue = \"GET, POST, PUT, DELETE, OPTIONS\"\n\n[[deployment.responseHeaders]]\npath = \"/*\"\nname = \"Access-Control-Allow-Headers\"\nvalue = \"Content-Type, Authorization\"\n\n[[ports]]\nlocalPort = 80\nexternalPort = 80\n\n[[ports]]\nlocalPort = 3000\nexternalPort = 3000\n\n[[ports]]\nlocalPort = 5000\nexternalPort = 5000\n\n[[ports]]\nlocalPort = 24678\nexternalPort = 24678\n\n# Workflow for the \"Run\" button in the Replit IDE.\n[workflows]\nrunButton = \"Project\"\n\n[[workflows.workflow]]\nname = \"Project\"\nmode = \"parallel\"\nauthor = \"agent\"\n[[workflows.workflow.tasks]]\ntask = \"workflow.run\"\nargs = \"Start application\"\n\n[[workflows.workflow]]\nname = \"Start application\"\nauthor = \"agent\"\n[[workflows.workflow.tasks]]\ntask = \"shell.exec\"\nargs = \"npm run dev\" # This executes your development script.\nwaitForPort = 3000    # Waits for the Vite frontend dev server to be ready on port 3000.\n", "path": ".replit", "language": "plaintext", "prefixBegin": 0, "suffixEnd": 0}}], ["febf1747-b9d6-44d0-bfbd-4ceb14a58734", {"value": {"selectedCode": "", "prefix": "# ChewyAI Documentation\n\nWelcome to the ChewyAI documentation. This folder contains comprehensive documentation for the ChewyAI full-stack application.\n\n## 📁 Documentation Structure\n\n- **[RULES.md](./RULES.md)** - Development rules, coding standards, and best practices\n- **[MEMORIES.md](./MEMORIES.md)** - Important system decisions and architectural choices\n- **[DEPLOYMENT.md](./DEPLOYMENT.md)** - Production deployment guide and configuration\n- **[SECURITY.md](./SECURITY.md)** - Security practices and API key management\n- **[API.md](./API.md)** - API documentation and endpoint reference\n\n## 🚀 Quick Start\n\n### Development\n```bash\nnpm run dev\n```\n\n### Production Build\n```bash\nnpm run build\nnpm run start\n```\n\n### Testing Build\n```bash\nnpm run test:build\n```\n\n## 🏗️ Architecture Overview\n\nChewyAI is a full-stack application with:\n\n- **Frontend**: React + Vite + TypeScript + Tailwindcss + React Icons\n", "suffix": "- **Backend**: Express.js + Node.js\n- **Database**: Supabase (PostgreSQL)\n- **AI Integration**: OpenRouter API (user-configured)\n- **Deployment**: Replit (production-ready)\n\n## 🔐 Security\n\n- All API keys are handled ephemerally by the backend\n- No sensitive credentials are exposed to the client\n- Proper CORS and security headers implemented\n- Environment variables used for all configuration\n\n## 📝 Contributing\n\nPlease read [RULES.md](./RULES.md) before contributing to understand our development standards and practices.\n", "path": "docs/README.md", "language": "markdown", "prefixBegin": 0, "suffixEnd": 0}}], ["b26dfb10-585b-4d37-aaba-a6acb287e2b5", {"value": {"selectedCode": "### Database Strategy (2024-01-XX)", "prefix": "# ChewyAI System Memories\n\n## 🧠 Important Architectural Decisions\n\n### Security Architecture (2024-01-XX)\n**Decision**: Implement ephemeral API key handling for user AI provider credentials\n**Rationale**: \n- Users maintain control over their AI usage and costs\n- No liability for ChewyAI regarding AI provider billing\n- Enhanced security by not storing user credentials\n- Compliance with data protection principles\n\n**Implementation**:\n- User AI credentials sent to backend per request\n- In-memory only processing, no persistent storage\n- No logging of sensitive credentials\n- Backend validates and forwards to AI provider\n\n### Full-Stack Architecture (2024-01-XX)\n**Decision**: Single-server deployment with Express.js serving both API and static files\n**Rationale**:\n- Simplified deployment on Replit\n- Reduced complexity compared to separate frontend/backend deployments\n- Better performance with single-origin requests\n- Easier CORS management\n\n**Implementation**:\n- Vite builds React app to `dist/public/`\n- Express serves static files with proper caching\n- API routes prefixed with `/api` to avoid conflicts\n- SPA fallback routing for client-side navigation\n\n", "suffix": "\n**Decision**: Use Supabase as primary database with Row Level Security\n**Rationale**:\n- Built-in authentication and authorization\n- Real-time capabilities for future features\n- Managed PostgreSQL with good performance\n- Row Level Security for data isolation\n\n**Implementation**:\n- Supabase client for frontend authentication\n- Service role key for backend operations\n- RLS policies enforce user data access\n- JWT tokens for API authentication\n\n### Build System (2024-01-XX)\n**Decision**: Vite for frontend, esbuild for backend bundling\n**Rationale**:\n- Fast development builds with Vite\n- Optimized production builds with code splitting\n- Single JavaScript bundle for easy deployment\n- TypeScript support throughout\n\n**Implementation**:\n- Vite handles React app with optimizations\n- esbuild bundles server code with minification\n- Separate build steps for frontend and backend\n- Production-ready asset optimization\n\n## 🔧 Technical Implementation Details\n\n### API Route Organization\n**Current Structure**:\n- `/api/health` - Health checks and monitoring\n- `/api/flashcards/*` - Flashcard CRUD operations\n- `/api/quizzes/*` - Quiz management and generation\n- `/api/documents/*` - Document processing and storage\n- `/api/ai/*` - AI integration endpoints\n\n**Design Principles**:\n- RESTful API design where applicable\n- Consistent error response format\n- Proper HTTP status codes\n- Input validation with Zod schemas\n\n### Environment Variable Strategy\n**Development vs Production**:\n- Development: Fallback values for quick setup\n- Production: Required environment variables with validation\n- Client variables prefixed with `VITE_`\n- Server variables without prefix\n\n**Security Considerations**:\n- No sensitive fallbacks in production\n- Startup validation for required variables\n- Clear separation of client/server configuration\n\n### Static File Serving Strategy\n**Caching Strategy**:\n- HTML files: No cache (always fresh)\n- JS/CSS assets: 1 year cache with immutable flag\n- Images and fonts: Standard caching\n- API responses: No cache by default\n\n**Security Headers**:\n- Content Security Policy for XSS protection\n- Frame options to prevent clickjacking\n- Content type sniffing prevention\n- Referrer policy for privacy\n\n## 🚀 Deployment Decisions\n\n### Replit Configuration\n**Deployment Target**: Autoscale\n**Rationale**: \n- Automatic scaling based on traffic\n- Cost-effective for variable usage\n- Built-in load balancing\n- Easy environment management\n\n**Build Process**:\n- Single build command: `npm run build`\n- Health check endpoint: `/api/health`\n- Environment variables in deployment config\n- Automatic HTTPS and domain management\n\n### Production Optimizations\n**Frontend Optimizations**:\n- Code splitting by vendor and route\n- Asset hashing for cache busting\n- Tree shaking for smaller bundles\n- Lazy loading for non-critical components\n\n**Backend Optimizations**:\n- Minified server bundle\n- External package handling\n- Efficient static file serving\n- Proper error handling without stack traces\n\n## 📝 Development Workflow Decisions\n\n### Package Management\n**Decision**: Use npm as primary package manager\n**Rationale**:\n- Consistent with Node.js ecosystem\n- Good lock file support\n- Wide compatibility\n- Replit native support\n\n### TypeScript Configuration\n**Decision**: Strict TypeScript throughout the application\n**Rationale**:\n- Better developer experience\n- Catch errors at compile time\n- Improved code documentation\n- Better IDE support\n\n### Testing Strategy\n**Current Approach**: Build verification and manual testing\n**Future Considerations**:\n- Unit tests for critical business logic\n- Integration tests for API endpoints\n- End-to-end tests for user workflows\n- Performance testing for production loads\n\n## 🔄 Evolution and Future Considerations\n\n### Scalability Considerations\n- Database connection pooling for high traffic\n- CDN integration for static assets\n- Caching layer for frequently accessed data\n- Microservices migration if needed\n\n### Security Enhancements\n- Rate limiting for API endpoints\n- Advanced monitoring and alerting\n- Automated security scanning\n- Regular dependency updates\n\n### Feature Expansion\n- Real-time collaboration features\n- Advanced AI model support\n- Mobile application development\n- Offline functionality\n\n## 📊 Performance Benchmarks\n\n### Current Performance Targets\n- Page load time: < 2 seconds\n- API response time: < 500ms\n- Build time: < 2 minutes\n- Bundle size: < 1MB (gzipped)\n\n### Monitoring Metrics\n- Health check response time\n- Error rates by endpoint\n- User authentication success rate\n- AI provider integration reliability\n", "path": "docs/MEMORIES.md", "language": "markdown", "prefixBegin": 0, "suffixEnd": 0}}], ["4bab5a7e-e712-422f-8290-f94bba478b08", {"value": {"selectedCode": "", "prefix": "# ChewyAI System Memories\n\n## 🧠 Important Architectural Decisions\n\n### Security Architecture (2025-06-02)\n**Decision**: Implement ephemeral API key handling for user AI provider credentials\n**Rationale**: \n- Users maintain control over their AI usage and costs\n- No liability for ChewyAI regarding AI provider billing\n- Enhanced security by not storing user credentials\n- Compliance with data protection principles\n\n**Implementation**:\n- User AI credentials sent to backend per request\n- In-memory only processing, no persistent storage\n- No logging of sensitive credentials\n- Backend validates and forwards to AI provider\n\n### Full-Stack Architecture (2025-06-02)\n**Decision**: Single-server deployment with Express.js serving both API and static files\n**Rationale**:\n- Simplified deployment on Replit\n- Reduced complexity compared to separate frontend/backend deployments\n- Better performance with single-origin requests\n- Easier CORS management\n\n**Implementation**:\n- Vite builds React app to `dist/public/`\n- Express serves static files with proper caching\n- API routes prefixed with `/api` to avoid conflicts\n- SPA fallback routing for client-side navigation\n\n### Database Strategy (2025-06-02)\n", "suffix": "**Decision**: Use Supabase as primary database with Row Level Security\n**Rationale**:\n- Built-in authentication and authorization\n- Real-time capabilities for future features\n- Managed PostgreSQL with good performance\n- Row Level Security for data isolation\n\n**Implementation**:\n- Supabase client for frontend authentication\n- Service role key for backend operations\n- RLS policies enforce user data access\n- JWT tokens for API authentication\n\n### Build System (2025-06-02)\n**Decision**: Vite for frontend, esbuild for backend bundling\n**Rationale**:\n- Fast development builds with Vite\n- Optimized production builds with code splitting\n- Single JavaScript bundle for easy deployment\n- TypeScript support throughout\n\n**Implementation**:\n- Vite handles React app with optimizations\n- esbuild bundles server code with minification\n- Separate build steps for frontend and backend\n- Production-ready asset optimization\n\n## 🔧 Technical Implementation Details\n\n### API Route Organization\n**Current Structure**:\n- `/api/health` - Health checks and monitoring\n- `/api/flashcards/*` - Flashcard CRUD operations\n- `/api/quizzes/*` - Quiz management and generation\n- `/api/documents/*` - Document processing and storage\n- `/api/ai/*` - AI integration endpoints\n\n**Design Principles**:\n- RESTful API design where applicable\n- Consistent error response format\n- Proper HTTP status codes\n- Input validation with Zod schemas\n\n### Environment Variable Strategy\n**Development vs Production**:\n- Development: Fallback values for quick setup\n- Production: Required environment variables with validation\n- Client variables prefixed with `VITE_`\n- Server variables without prefix\n\n**Security Considerations**:\n- No sensitive fallbacks in production\n- Startup validation for required variables\n- Clear separation of client/server configuration\n\n### Static File Serving Strategy\n**Caching Strategy**:\n- HTML files: No cache (always fresh)\n- JS/CSS assets: 1 year cache with immutable flag\n- Images and fonts: Standard caching\n- API responses: No cache by default\n\n**Security Headers**:\n- Content Security Policy for XSS protection\n- Frame options to prevent clickjacking\n- Content type sniffing prevention\n- Referrer policy for privacy\n\n## 🚀 Deployment Decisions\n\n### Replit Configuration\n**Deployment Target**: Autoscale\n**Rationale**: \n- Automatic scaling based on traffic\n- Cost-effective for variable usage\n- Built-in load balancing\n- Easy environment management\n\n**Build Process**:\n- Single build command: `npm run build`\n- Health check endpoint: `/api/health`\n- Environment variables in deployment config\n- Automatic HTTPS and domain management\n\n### Production Optimizations\n**Frontend Optimizations**:\n- Code splitting by vendor and route\n- Asset hashing for cache busting\n- Tree shaking for smaller bundles\n- Lazy loading for non-critical components\n\n**Backend Optimizations**:\n- Minified server bundle\n- External package handling\n- Efficient static file serving\n- Proper error handling without stack traces\n\n## 📝 Development Workflow Decisions\n\n### Package Management\n**Decision**: Use npm as primary package manager\n**Rationale**:\n- Consistent with Node.js ecosystem\n- Good lock file support\n- Wide compatibility\n- Replit native support\n\n### TypeScript Configuration\n**Decision**: Strict TypeScript throughout the application\n**Rationale**:\n- Better developer experience\n- Catch errors at compile time\n- Improved code documentation\n- Better IDE support\n\n### Testing Strategy\n**Current Approach**: Build verification and manual testing\n**Future Considerations**:\n- Unit tests for critical business logic\n- Integration tests for API endpoints\n- End-to-end tests for user workflows\n- Performance testing for production loads\n\n## 🔄 Evolution and Future Considerations\n\n### Scalability Considerations\n- Database connection pooling for high traffic\n- CDN integration for static assets\n- Caching layer for frequently accessed data\n- Microservices migration if needed\n\n### Security Enhancements\n- Rate limiting for API endpoints\n- Advanced monitoring and alerting\n- Automated security scanning\n- Regular dependency updates\n\n### Feature Expansion\n- Real-time collaboration features\n- Advanced AI model support\n- Mobile application development\n- Offline functionality\n\n## 📊 Performance Benchmarks\n\n### Current Performance Targets\n- Page load time: < 2 seconds\n- API response time: < 500ms\n- Build time: < 2 minutes\n- Bundle size: < 1MB (gzipped)\n\n### Monitoring Metrics\n- Health check response time\n- Error rates by endpoint\n- User authentication success rate\n- AI provider integration reliability\n", "path": "docs/MEMORIES.md", "language": "markdown", "prefixBegin": 0, "suffixEnd": 0}}], ["2fec49e2-1160-47bd-bb7b-c2b0dc034f2d", {"value": {"selectedCode": "- Images and fonts: Standard caching", "prefix": "# ChewyAI System Memories\n\n## 🧠 Important Architectural Decisions\n\n### Security Architecture (2025-06-02)\n**Decision**: Implement ephemeral API key handling for user AI provider credentials\n**Rationale**: \n- Users maintain control over their AI usage and costs\n- No liability for ChewyAI regarding AI provider billing\n- Enhanced security by not storing user credentials\n- Compliance with data protection principles\n\n**Implementation**:\n- User AI credentials sent to backend per request\n- In-memory only processing, no persistent storage\n- No logging of sensitive credentials\n- Backend validates and forwards to AI provider\n\n### Full-Stack Architecture (2025-06-02)\n**Decision**: Single-server deployment with Express.js serving both API and static files\n**Rationale**:\n- Simplified deployment on Replit\n- Reduced complexity compared to separate frontend/backend deployments\n- Better performance with single-origin requests\n- Easier CORS management\n\n**Implementation**:\n- Vite builds React app to `dist/public/`\n- Express serves static files with proper caching\n- API routes prefixed with `/api` to avoid conflicts\n- SPA fallback routing for client-side navigation\n\n### Database Strategy (2025-06-02)\n**Decision**: Use Supabase as primary database with Row Level Security\n**Rationale**:\n- Built-in authentication and authorization\n- Real-time capabilities for future features\n- Managed PostgreSQL with good performance\n- Row Level Security for data isolation\n\n**Implementation**:\n- Supabase client for frontend authentication\n- Service role key for backend operations\n- RLS policies enforce user data access\n- JWT tokens for API authentication\n\n### Build System (2025-06-02)\n**Decision**: Vite for frontend, esbuild for backend bundling\n**Rationale**:\n- Fast development builds with Vite\n- Optimized production builds with code splitting\n- Single JavaScript bundle for easy deployment\n- TypeScript support throughout\n\n**Implementation**:\n- Vite handles React app with optimizations\n- esbuild bundles server code with minification\n- Separate build steps for frontend and backend\n- Production-ready asset optimization\n\n## 🔧 Technical Implementation Details\n\n### API Route Organization\n**Current Structure**:\n- `/api/health` - Health checks and monitoring\n- `/api/flashcards/*` - Flashcard CRUD operations\n- `/api/quizzes/*` - Quiz management and generation\n- `/api/documents/*` - Document processing and storage\n- `/api/ai/*` - AI integration endpoints\n\n**Design Principles**:\n- RESTful API design where applicable\n- Consistent error response format\n- Proper HTTP status codes\n- Input validation with Zod schemas\n\n### Environment Variable Strategy\n**Development vs Production**:\n- Development: Fallback values for quick setup\n- Production: Required environment variables with validation\n- Client variables prefixed with `VITE_`\n- Server variables without prefix\n\n**Security Considerations**:\n- No sensitive fallbacks in production\n- Startup validation for required variables\n- Clear separation of client/server configuration\n\n### Static File Serving Strategy\n**Caching Strategy**:\n- HTML files: No cache (always fresh)\n- JS/CSS assets: 1 year cache with immutable flag\n", "suffix": "\n- API responses: No cache by default\n\n**Security Headers**:\n- Content Security Policy for XSS protection\n- Frame options to prevent clickjacking\n- Content type sniffing prevention\n- Referrer policy for privacy\n\n## 🚀 Deployment Decisions\n\n### Replit Configuration\n**Deployment Target**: Autoscale\n**Rationale**: \n- Automatic scaling based on traffic\n- Cost-effective for variable usage\n- Built-in load balancing\n- Easy environment management\n\n**Build Process**:\n- Single build command: `npm run build`\n- Health check endpoint: `/api/health`\n- Environment variables in deployment config\n- Automatic HTTPS and domain management\n\n### Production Optimizations\n**Frontend Optimizations**:\n- Code splitting by vendor and route\n- Asset hashing for cache busting\n- Tree shaking for smaller bundles\n- Lazy loading for non-critical components\n\n**Backend Optimizations**:\n- Minified server bundle\n- External package handling\n- Efficient static file serving\n- Proper error handling without stack traces\n\n## 📝 Development Workflow Decisions\n\n### Package Management\n**Decision**: Use npm as primary package manager\n**Rationale**:\n- Consistent with Node.js ecosystem\n- Good lock file support\n- Wide compatibility\n- Replit native support\n\n### TypeScript Configuration\n**Decision**: Strict TypeScript throughout the application\n**Rationale**:\n- Better developer experience\n- Catch errors at compile time\n- Improved code documentation\n- Better IDE support\n\n### Testing Strategy\n**Current Approach**: Build verification and manual testing\n**Future Considerations**:\n- Unit tests for critical business logic\n- Integration tests for API endpoints\n- End-to-end tests for user workflows\n- Performance testing for production loads\n\n## 🔄 Evolution and Future Considerations\n\n### Scalability Considerations\n- Database connection pooling for high traffic\n- CDN integration for static assets\n- Caching layer for frequently accessed data\n- Microservices migration if needed\n\n### Security Enhancements\n- Rate limiting for API endpoints\n- Advanced monitoring and alerting\n- Automated security scanning\n- Regular dependency updates\n\n### Feature Expansion\n- Real-time collaboration features\n- Advanced AI model support\n- Mobile application development\n- Offline functionality\n\n## 📊 Performance Benchmarks\n\n### Current Performance Targets\n- Page load time: < 2 seconds\n- API response time: < 500ms\n- Build time: < 2 minutes\n- Bundle size: < 1MB (gzipped)\n\n### Monitoring Metrics\n- Health check response time\n- Error rates by endpoint\n- User authentication success rate\n- AI provider integration reliability\n", "path": "docs/MEMORIES.md", "language": "markdown", "prefixBegin": 0, "suffixEnd": 0}}], ["5f41da80-bb39-4bd6-ab8c-85675ac452e0", {"value": {"selectedCode": "", "prefix": "# ChewyAI System Memories\n\n## 🧠 Important Architectural Decisions\n\n### Security Architecture (2025-06-02)\n**Decision**: Implement ephemeral API key handling for user AI provider credentials\n**Rationale**: \n- Users maintain control over their AI usage and costs\n- No liability for ChewyAI regarding AI provider billing\n- Enhanced security by not storing user credentials\n- Compliance with data protection principles\n\n**Implementation**:\n- User AI credentials sent to backend per request\n- In-memory only processing, no persistent storage\n- No logging of sensitive credentials\n- Backend validates and forwards to AI provider\n\n### Full-Stack Architecture (2025-06-02)\n**Decision**: Single-server deployment with Express.js serving both API and static files\n**Rationale**:\n- Simplified deployment on Replit\n- Reduced complexity compared to separate frontend/backend deployments\n- Better performance with single-origin requests\n- Easier CORS management\n\n**Implementation**:\n- Vite builds React app to `dist/public/`\n- Express serves static files with proper caching\n- API routes prefixed with `/api` to avoid conflicts\n- SPA fallback routing for client-side navigation\n\n### Database Strategy (2025-06-02)\n**Decision**: Use Supabase as primary database with Row Level Security\n**Rationale**:\n- Built-in authentication and authorization\n- Real-time capabilities for future features\n- Managed PostgreSQL with good performance\n- Row Level Security for data isolation\n\n**Implementation**:\n- Supabase client for frontend authentication\n- Service role key for backend operations\n- RLS policies enforce user data access\n- JWT tokens for API authentication\n\n### Build System (2025-06-02)\n**Decision**: Vite for frontend, esbuild for backend bundling\n**Rationale**:\n- Fast development builds with Vite\n- Optimized production builds with code splitting\n- Single JavaScript bundle for easy deployment\n- TypeScript support throughout\n\n**Implementation**:\n- Vite handles React app with optimizations\n- esbuild bundles server code with minification\n- Separate build steps for frontend and backend\n- Production-ready asset optimization\n\n## 🔧 Technical Implementation Details\n\n### API Route Organization\n**Current Structure**:\n- `/api/health` - Health checks and monitoring\n- `/api/flashcards/*` - Flashcard CRUD operations\n- `/api/quizzes/*` - Quiz management and generation\n- `/api/documents/*` - Document processing and storage\n- `/api/ai/*` - AI integration endpoints\n\n**Design Principles**:\n- RESTful API design where applicable\n- Consistent error response format\n- Proper HTTP status codes\n- Input validation with Zod schemas\n\n### Environment Variable Strategy\n**Development vs Production**:\n- Development: Fallback values for quick setup\n- Production: Required environment variables with validation\n- Client variables prefixed with `VITE_`\n- Server variables without prefix\n\n**Security Considerations**:\n- No sensitive fallbacks in production\n- Startup validation for required variables\n- Clear separation of client/server configuration\n\n### Static File Serving Strategy\n**Caching Strategy**:\n- HTML files: No cache (always fresh)\n- JS/CSS assets: 1 year cache with immutable flag\n- Images and fonts: Standard caching\n- API responses: No cache by default\n\n**Security Headers**:\n- Content Security Policy for XSS protection\n- Frame options to prevent clickjacking\n- Content type sniffing prevention\n- Referrer policy for privacy\n\n## 🚀 Deployment Decisions\n\n### Replit Configuration\n**Deployment Target**: Autoscale\n**Rationale**: \n", "suffix": "- Automatic scaling based on traffic\n- Cost-effective for variable usage\n- Built-in load balancing\n- Easy environment management\n\n**Build Process**:\n- Single build command: `npm run build`\n- Health check endpoint: `/api/health`\n- Environment variables in deployment config\n- Automatic HTTPS and domain management\n\n### Production Optimizations\n**Frontend Optimizations**:\n- Code splitting by vendor and route\n- Asset hashing for cache busting\n- Tree shaking for smaller bundles\n- Lazy loading for non-critical components\n\n**Backend Optimizations**:\n- Minified server bundle\n- External package handling\n- Efficient static file serving\n- Proper error handling without stack traces\n\n## 📝 Development Workflow Decisions\n\n### Package Management\n**Decision**: Use npm as primary package manager\n**Rationale**:\n- Consistent with Node.js ecosystem\n- Good lock file support\n- Wide compatibility\n- Replit native support\n\n### TypeScript Configuration\n**Decision**: Strict TypeScript throughout the application\n**Rationale**:\n- Better developer experience\n- Catch errors at compile time\n- Improved code documentation\n- Better IDE support\n\n### Testing Strategy\n**Current Approach**: Build verification and manual testing\n**Future Considerations**:\n- Unit tests for critical business logic\n- Integration tests for API endpoints\n- End-to-end tests for user workflows\n- Performance testing for production loads\n\n## 🔄 Evolution and Future Considerations\n\n### Scalability Considerations\n- Database connection pooling for high traffic\n- CDN integration for static assets\n- Caching layer for frequently accessed data\n- Microservices migration if needed\n\n### Security Enhancements\n- Rate limiting for API endpoints\n- Advanced monitoring and alerting\n- Automated security scanning\n- Regular dependency updates\n\n### Feature Expansion\n- Real-time collaboration features\n- Advanced AI model support\n- Mobile application development\n- Offline functionality\n\n## 📊 Performance Benchmarks\n\n### Current Performance Targets\n- Page load time: < 2 seconds\n- API response time: < 500ms\n- Build time: < 2 minutes\n- Bundle size: < 1MB (gzipped)\n\n### Monitoring Metrics\n- Health check response time\n- Error rates by endpoint\n- User authentication success rate\n- AI provider integration reliability\n", "path": "docs/MEMORIES.md", "language": "markdown", "prefixBegin": 0, "suffixEnd": 0}}], ["3dbc5c1f-f61e-44f5-a61a-51856e29925a", {"value": {"selectedCode": "", "prefix": "# ChewyAI System Memories\n\n## 🧠 Important Architectural Decisions\n\n### Security Architecture (2025-06-02)\n**Decision**: Implement ephemeral API key handling for user AI provider credentials\n**Rationale**: \n- Users maintain control over their AI usage and costs\n- No liability for ChewyAI regarding AI provider billing\n- Enhanced security by not storing user credentials\n- Compliance with data protection principles\n\n**Implementation**:\n- User AI credentials sent to backend per request\n- In-memory only processing, no persistent storage\n- No logging of sensitive credentials\n- Backend validates and forwards to AI provider\n\n### Full-Stack Architecture (2025-06-02)\n**Decision**: Single-server deployment with Express.js serving both API and static files\n**Rationale**:\n- Simplified deployment on Replit\n- Reduced complexity compared to separate frontend/backend deployments\n- Better performance with single-origin requests\n- Easier CORS management\n\n**Implementation**:\n- Vite builds React app to `dist/public/`\n- Express serves static files with proper caching\n- API routes prefixed with `/api` to avoid conflicts\n- SPA fallback routing for client-side navigation\n\n### Database Strategy (2025-06-02)\n**Decision**: Use Supabase as primary database with Row Level Security\n**Rationale**:\n- Built-in authentication and authorization\n- Real-time capabilities for future features\n- Managed PostgreSQL with good performance\n- Row Level Security for data isolation\n\n**Implementation**:\n- Supabase client for frontend authentication\n- Service role key for backend operations\n- RLS policies enforce user data access\n- JWT tokens for API authentication\n\n### Build System (2025-06-02)\n**Decision**: Vite for frontend, esbuild for backend bundling\n**Rationale**:\n- Fast development builds with Vite\n- Optimized production builds with code splitting\n- Single JavaScript bundle for easy deployment\n- TypeScript support throughout\n\n**Implementation**:\n- Vite handles React app with optimizations\n- esbuild bundles server code with minification\n- Separate build steps for frontend and backend\n- Production-ready asset optimization\n\n## 🔧 Technical Implementation Details\n\n### API Route Organization\n**Current Structure**:\n- `/api/health` - Health checks and monitoring\n- `/api/flashcards/*` - Flashcard CRUD operations\n- `/api/quizzes/*` - Quiz management and generation\n- `/api/documents/*` - Document processing and storage\n- `/api/ai/*` - AI integration endpoints\n\n**Design Principles**:\n- RESTful API design where applicable\n- Consistent error response format\n- Proper HTTP status codes\n- Input validation with Zod schemas\n\n### Environment Variable Strategy\n**Development vs Production**:\n- Development: Fallback values for quick setup\n- Production: Required environment variables with validation\n- Client variables prefixed with `VITE_`\n- Server variables without prefix\n\n**Security Considerations**:\n- No sensitive fallbacks in production\n- Startup validation for required variables\n- Clear separation of client/server configuration\n\n### Static File Serving Strategy\n**Caching Strategy**:\n- HTML files: No cache (always fresh)\n- JS/CSS assets: 1 year cache with immutable flag\n", "suffix": "- Images and fonts: Standard caching\n- API responses: No cache by default\n\n**Security Headers**:\n- Content Security Policy for XSS protection\n- Frame options to prevent clickjacking\n- Content type sniffing prevention\n- Referrer policy for privacy\n\n## 🚀 Deployment Decisions\n\n### Replit Configuration\n**Deployment Target**: Autoscale\n**Rationale**: \n- Automatic scaling based on traffic\n- Cost-effective for variable usage\n- Built-in load balancing\n- Easy environment management\n\n**Build Process**:\n- Single build command: `npm run build`\n- Health check endpoint: `/api/health`\n- Environment variables in deployment config\n- Automatic HTTPS and domain management\n\n### Production Optimizations\n**Frontend Optimizations**:\n- Code splitting by vendor and route\n- Asset hashing for cache busting\n- Tree shaking for smaller bundles\n- Lazy loading for non-critical components\n\n**Backend Optimizations**:\n- Minified server bundle\n- External package handling\n- Efficient static file serving\n- Proper error handling without stack traces\n\n## 📝 Development Workflow Decisions\n\n### Package Management\n**Decision**: Use npm as primary package manager\n**Rationale**:\n- Consistent with Node.js ecosystem\n- Good lock file support\n- Wide compatibility\n- Replit native support\n\n### TypeScript Configuration\n**Decision**: Strict TypeScript throughout the application\n**Rationale**:\n- Better developer experience\n- Catch errors at compile time\n- Improved code documentation\n- Better IDE support\n\n### Testing Strategy\n**Current Approach**: Build verification and manual testing\n**Future Considerations**:\n- Unit tests for critical business logic\n- Integration tests for API endpoints\n- End-to-end tests for user workflows\n- Performance testing for production loads\n\n## 🔄 Evolution and Future Considerations\n\n### Scalability Considerations\n- Database connection pooling for high traffic\n- CDN integration for static assets\n- Caching layer for frequently accessed data\n- Microservices migration if needed\n\n### Security Enhancements\n- Rate limiting for API endpoints\n- Advanced monitoring and alerting\n- Automated security scanning\n- Regular dependency updates\n\n### Feature Expansion\n- Real-time collaboration features\n- Advanced AI model support\n- Mobile application development\n- Offline functionality\n\n## 📊 Performance Benchmarks\n\n### Current Performance Targets\n- Page load time: < 2 seconds\n- API response time: < 500ms\n- Build time: < 2 minutes\n- Bundle size: < 1MB (gzipped)\n\n### Monitoring Metrics\n- Health check response time\n- Error rates by endpoint\n- User authentication success rate\n- AI provider integration reliability\n", "path": "docs/MEMORIES.md", "language": "markdown", "prefixBegin": 0, "suffixEnd": 0}}], ["7c5cb33b-30f5-4de0-b4c8-08780f6a0a87", {"value": {"selectedCode": "", "prefix": "# ChewyAI System Memories\n\n## 🧠 Important Architectural Decisions\n\n### Security Architecture (2025-06-02)\n**Decision**: Implement ephemeral API key handling for user AI provider credentials\n**Rationale**: \n- Users maintain control over their AI usage and costs\n- No liability for ChewyAI regarding AI provider billing\n- Enhanced security by not storing user credentials\n- Compliance with data protection principles\n\n**Implementation**:\n- User AI credentials sent to backend per request\n- In-memory only processing, no persistent storage\n- No logging of sensitive credentials\n- Backend validates and forwards to AI provider\n\n### Full-Stack Architecture (2025-06-02)\n**Decision**: Single-server deployment with Express.js serving both API and static files\n**Rationale**:\n- Simplified deployment on Replit\n- Reduced complexity compared to separate frontend/backend deployments\n- Better performance with single-origin requests\n- Easier CORS management\n\n**Implementation**:\n- Vite builds React app to `dist/public/`\n- Express serves static files with proper caching\n- API routes prefixed with `/api` to avoid conflicts\n- SPA fallback routing for client-side navigation\n\n### Database Strategy (2025-06-02)\n**Decision**: Use Supabase as primary database with Row Level Security\n**Rationale**:\n- Built-in authentication and authorization\n- Real-time capabilities for future features\n- Managed PostgreSQL with good performance\n- Row Level Security for data isolation\n\n**Implementation**:\n- Supabase client for frontend authentication\n- Service role key for backend operations\n- RLS policies enforce user data access\n- JWT tokens for API authentication\n\n### Build System (2025-06-02)\n**Decision**: Vite for frontend, esbuild for backend bundling\n**Rationale**:\n- Fast development builds with Vite\n- Optimized production builds with code splitting\n- Single JavaScript bundle for easy deployment\n- TypeScript support throughout\n\n**Implementation**:\n- Vite handles React app with optimizations\n- esbuild bundles server code with minification\n- Separate build steps for frontend and backend\n- Production-ready asset optimization\n\n## 🔧 Technical Implementation Details\n\n### API Route Organization\n**Current Structure**:\n- `/api/health` - Health checks and monitoring\n- `/api/flashcards/*` - Flashcard CRUD operations\n- `/api/quizzes/*` - Quiz management and generation\n- `/api/documents/*` - Document processing and storage\n- `/api/ai/*` - AI integration endpoints\n\n**Design Principles**:\n- RESTful API design where applicable\n- Consistent error response format\n- Proper HTTP status codes\n- Input validation with Zod schemas\n\n### Environment Variable Strategy\n**Development vs Production**:\n- Development: Fallback values for quick setup\n- Production: Required environment variables with validation\n- Client variables prefixed with `VITE_`\n- Server variables without prefix\n\n**Security Considerations**:\n- No sensitive fallbacks in production\n- Startup validation for required variables\n- Clear separation of client/server configuration\n\n### Static File Serving Strategy\n**Caching Strategy**:\n- HTML files: No cache (always fresh)\n- JS/CSS asse ts: 1 year cache with immutable flag\n", "suffix": "- Images and fonts: Standard caching\n- API responses: No cache by default\n\n**Security Headers**:\n- Content Security Policy for XSS protection\n- Frame options to prevent clickjacking\n- Content type sniffing prevention\n- Referrer policy for privacy\n\n## 🚀 Deployment Decisions\n\n### Replit Configuration\n**Deployment Target**: Autoscale\n**Rationale**: \n- Automatic scaling based on traffic\n- Cost-effective for variable usage\n- Built-in load balancing\n- Easy environment management\n\n**Build Process**:\n- Single build command: `npm run build`\n- Health check endpoint: `/api/health`\n- Environment variables in deployment config\n- Automatic HTTPS and domain management\n\n### Production Optimizations\n**Frontend Optimizations**:\n- Code splitting by vendor and route\n- Asset hashing for cache busting\n- Tree shaking for smaller bundles\n- Lazy loading for non-critical components\n\n**Backend Optimizations**:\n- Minified server bundle\n- External package handling\n- Efficient static file serving\n- Proper error handling without stack traces\n\n## 📝 Development Workflow Decisions\n\n### Package Management\n**Decision**: Use npm as primary package manager\n**Rationale**:\n- Consistent with Node.js ecosystem\n- Good lock file support\n- Wide compatibility\n- Replit native support\n\n### TypeScript Configuration\n**Decision**: Strict TypeScript throughout the application\n**Rationale**:\n- Better developer experience\n- Catch errors at compile time\n- Improved code documentation\n- Better IDE support\n\n### Testing Strategy\n**Current Approach**: Build verification and manual testing\n**Future Considerations**:\n- Unit tests for critical business logic\n- Integration tests for API endpoints\n- End-to-end tests for user workflows\n- Performance testing for production loads\n\n## 🔄 Evolution and Future Considerations\n\n### Scalability Considerations\n- Database connection pooling for high traffic\n- CDN integration for static assets\n- Caching layer for frequently accessed data\n- Microservices migration if needed\n\n### Security Enhancements\n- Rate limiting for API endpoints\n- Advanced monitoring and alerting\n- Automated security scanning\n- Regular dependency updates\n\n### Feature Expansion\n- Real-time collaboration features\n- Advanced AI model support\n- Mobile application development\n- Offline functionality\n\n## 📊 Performance Benchmarks\n\n### Current Performance Targets\n- Page load time: < 2 seconds\n- API response time: < 500ms\n- Build time: < 2 minutes\n- Bundle size: < 1MB (gzipped)\n\n### Monitoring Metrics\n- Health check response time\n- Error rates by endpoint\n- User authentication success rate\n- AI provider integration reliability\n", "path": "docs/MEMORIES.md", "language": "markdown", "prefixBegin": 0, "suffixEnd": 0}}], ["f441292f-06b4-4dc8-8a6e-8a195b5c9242", {"value": {"selectedCode": "", "prefix": "# ChewyAI System Memories\n\n## 🧠 Important Architectural Decisions\n\n### Security Architecture (2025-06-02)\n**Decision**: Implement encrypted database storage for user AI provider credentials\n**Rationale**:\n- Users maintain control over their AI usage and costs\n- No liability for ChewyAI regarding AI provider billing\n- Enhanced security with AES-256-GCM encryption\n- Compliance with data protection principles\n- Better user experience (no need to re-enter credentials)\n\n**Implementation**:\n- User AI credentials encrypted with AES-256-GCM before database storage\n- Row Level Security (RLS) policies enforce user data isolation\n- API keys retrieved and decrypted only during AI API calls (ephemeral use)\n- No logging of sensitive credentials or headers\n- Client-side localStorage only stores non-sensitive configuration\n\n**Security Audit Results (2025-06-02)**:\n- Fixed critical hardcoded credentials vulnerability\n- Eliminated client-side API key exposure risks\n- Implemented secure encrypted credential storage\n- Enhanced authentication and error handling\n\n### Full-Stack Architecture (2025-06-02)\n**Decision**: Single-server deployment with Express.js serving both API and static files\n**Rationale**:\n- Simplified deployment on Replit\n- Reduced complexity compared to separate frontend/backend deployments\n- Better performance with single-origin requests\n- Easier CORS management\n\n**Implementation**:\n- Vite builds React app to `dist/public/`\n- Express serves static files with proper caching\n- API routes prefixed with `/api` to avoid conflicts\n- SPA fallback routing for client-side navigation\n\n### Database Strategy (2025-06-02)\n**Decision**: Use Supabase as primary database with Row Level Security\n**Rationale**:\n- Built-in authentication and authorization\n- Real-time capabilities for future features\n- Managed PostgreSQL with good performance\n- Row Level Security for data isolation\n\n**Implementation**:\n- Supabase client for frontend authentication\n- Service role key for backend operations\n- RLS policies enforce user data access\n- JWT tokens for API authentication\n\n### Build System (2025-06-02)\n**Decision**: Vite for frontend, esbuild for backend bundling\n**Rationale**:\n- Fast development builds with Vite\n- Optimized production builds with code splitting\n- Single JavaScript bundle for easy deployment\n- TypeScript support throughout\n\n**Implementation**:\n- Vite handles React app with optimizations\n- esbuild bundles server code with minification\n- Separate build steps for frontend and backend\n- Production-ready asset optimization\n\n## 🔧 Technical Implementation Details\n\n### API Route Organization\n**Current Structure**:\n- `/api/health` - Health checks and monitoring\n- `/api/flashcards/*` - Flashcard CRUD operations\n- `/api/quizzes/*` - Quiz management and generation\n- `/api/documents/*` - Document processing and storage\n- `/api/ai/*` - AI integration endpoints\n\n**Design Principles**:\n- RESTful API design where applicable\n- Consistent error response format\n- Proper HTTP status codes\n- Input validation with Zod schemas\n\n### Environment Variable Strategy\n**Development vs Production**:\n- Development: Fallback values for quick setup\n- Production: Required environment variables with validation\n- Client variables prefixed with `VITE_`\n- Server variables without prefix\n\n**Security Considerations**:\n- No sensitive fallbacks in production\n- Startup validation for required variables\n- Clear separation of client/server configuration\n\n### Static File Serving Strategy\n**Caching Strategy**:\n- HTML files: No cache (always fresh)\n- JS/CSS asse ts: 1 year cache with immutable flag\n", "suffix": "- Images and fonts: Standard caching\n- API responses: No cache by default\n\n**Security Headers**:\n- Content Security Policy for XSS protection\n- Frame options to prevent clickjacking\n- Content type sniffing prevention\n- Referrer policy for privacy\n\n## 🚀 Deployment Decisions\n\n### Replit Configuration\n**Deployment Target**: Autoscale\n**Rationale**: \n- Automatic scaling based on traffic\n- Cost-effective for variable usage\n- Built-in load balancing\n- Easy environment management\n\n**Build Process**:\n- Single build command: `npm run build`\n- Health check endpoint: `/api/health`\n- Environment variables in deployment config\n- Automatic HTTPS and domain management\n\n### Production Optimizations\n**Frontend Optimizations**:\n- Code splitting by vendor and route\n- Asset hashing for cache busting\n- Tree shaking for smaller bundles\n- Lazy loading for non-critical components\n\n**Backend Optimizations**:\n- Minified server bundle\n- External package handling\n- Efficient static file serving\n- Proper error handling without stack traces\n\n## 📝 Development Workflow Decisions\n\n### Package Management\n**Decision**: Use npm as primary package manager\n**Rationale**:\n- Consistent with Node.js ecosystem\n- Good lock file support\n- Wide compatibility\n- Replit native support\n\n### TypeScript Configuration\n**Decision**: Strict TypeScript throughout the application\n**Rationale**:\n- Better developer experience\n- Catch errors at compile time\n- Improved code documentation\n- Better IDE support\n\n### Testing Strategy\n**Current Approach**: Build verification and manual testing\n**Future Considerations**:\n- Unit tests for critical business logic\n- Integration tests for API endpoints\n- End-to-end tests for user workflows\n- Performance testing for production loads\n\n## 🔄 Evolution and Future Considerations\n\n### Scalability Considerations\n- Database connection pooling for high traffic\n- CDN integration for static assets\n- Caching layer for frequently accessed data\n- Microservices migration if needed\n\n### Security Enhancements\n- Rate limiting for API endpoints\n- Advanced monitoring and alerting\n- Automated security scanning\n- Regular dependency updates\n\n### Feature Expansion\n- Real-time collaboration features\n- Advanced AI model support\n- Mobile application development\n- Offline functionality\n\n## 📊 Performance Benchmarks\n\n### Current Performance Targets\n- Page load time: < 2 seconds\n- API response time: < 500ms\n- Build time: < 2 minutes\n- Bundle size: < 1MB (gzipped)\n\n### Monitoring Metrics\n- Health check response time\n- Error rates by endpoint\n- User authentication success rate\n- AI provider integration reliability\n", "path": "docs/MEMORIES.md", "language": "markdown", "prefixBegin": 0, "suffixEnd": 0}}], ["c2d32f84-c7d0-4fd3-8c1f-5442bd79ebef", {"value": {"selectedCode": "", "prefix": "# =============================================================================\n# ChewyAI Environment Configuration\n# =============================================================================\n# Copy this file to .env and fill in your actual values\n# NEVER commit .env files to version control\n\n# =============================================================================\n# CLIENT ENVIRONMENT VARIABLES (VITE_ prefix for Vite)\n# =============================================================================\n# These are exposed to the browser - only include non-sensitive values\n\n# Supabase Configuration (Client)\nVITE_SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co\nVITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.0qS0Nh1aV4GjZYniANoUZKDDcuCcQxzTm-DnTeZsNlQ\n\n# API Configuration\nVITE_API_BASE_URL=http://localhost:5000/api\n\n# =============================================================================\n# SERVER ENVIRONMENT VARIABLES\n# =============================================================================\n# These are only available on the server - include sensitive values here\n\n# Application Configuration\nNODE_ENV=development\nPORT=5000\n\n# Supabase Configuration (Server)\nSUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co\nSUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0\n\n# Database Configuration (if using direct database access)\nVITE_DATABASE_PASSWORD=Aloha808!\n\n# =============================================================================\n# AI PROVIDER CONFIGURATION (Optional)\n# =============================================================================\n# Default AI provider settings - users will configure their own in the app\n\nDEFAULT_AI_PROVIDER=openrouter\nDEFAULT_AI_BASE_URL=https://openrouter.ai/api/v1\nDEFAULT_EXTRACTION_MODEL=google/gemini-2.5-flash-preview-05-20\nDEFAULT_GENERATION_MODEL=google/gemini-2.5-pro-preview\n\n# =============================================================================\n# SECURITY CONFIGURATION\n# =============================================================================\n# Generate secure random strings for production\n\nJWT_SECRET=michelle\nSESSION_SECRET=2019\n\n# =============================================================================\n# CORS CONFIGURATION\n# =============================================================================\n# Frontend URL for CORS configuration\n\nFRONTEND_URL=http://localhost:3000\n\n# =============================================================================\n# ADDITIONAL DATABASE CONFIGURATION (Optional)\n# =============================================================================\n# For local SQLite storage if needed\n\nDATABASE_URL=./data/chewyai.sqlite\n\n# =============================================================================\n# PRODUCTION DEPLOYMENT NOTES\n# =============================================================================\n# For Replit deployment, set these in Replit Secrets:\n# - SUPABASE_URL\n", "suffix": "# - SUPABASE_SERVICE_ROLE_KEY\n# - VITE_SUPABASE_URL\n# - VITE_SUPABASE_ANON_KEY\n# - NODE_ENV=production\n# - PORT=80\n#\n# The VITE_ variables will be automatically included in the client build\n# The server variables will only be available to the backend", "path": ".env.example", "language": "shellscript", "prefixBegin": 0, "suffixEnd": 60}}], ["31bf9f7b-7b75-49d1-a9fc-a376c4682a4a", {"value": {"selectedCode": "", "prefix": "# ChewyAI System Memories\n\n## 🧠 Important Architectural Decisions\n\n### Security Architecture (2025-06-02)\n**Decision**: Implement encrypted database storage for user AI provider credentials\n**Rationale**:\n- Users maintain control over their AI usage and costs\n- No liability for ChewyAI regarding AI provider billing\n- Enhanced security with AES-256-GCM encryption\n- Compliance with data protection principles\n- Better user experience (no need to re-enter credentials)\n\n**Implementation**:\n- User AI credentials encrypted with AES-256-GCM before database storage\n- Row Level Security (RLS) policies enforce user data isolation\n- API keys retrieved and decrypted only during AI API calls (ephemeral use)\n- No logging of sensitive credentials or headers\n- Client-side localStorage only stores non-sensitive configuration\n\n**Security Audit Results (2025-06-02)**:\n- Fixed critical hardcoded credentials vulnerability\n- Eliminated client-side API key exposure risks\n- Implemented secure encrypted credential storage\n- Enhanced authentication and error handling\n\n### Full-Stack Architecture (2025-06-02)\n**Decision**: Single-server deployment with Express.js serving both API and static files\n**Rationale**:\n- Simplified deployment on Replit\n- Reduced complexity compared to separate frontend/backend deployments\n- Better performance with single-origin requests\n- Easier CORS management\n\n**Implementation**:\n- Vite builds React app to `dist/public/`\n- Express serves static files with proper caching\n- API routes prefixed with `/api` to avoid conflicts\n- SPA fallback routing for client-side navigation\n\n### Database Strategy (2025-06-02)\n**Decision**: Use Supabase as primary database with Row Level Security\n**Rationale**:\n- Built-in authentication and authorization\n- Real-time capabilities for future features\n- Managed PostgreSQL with good performance\n- Row Level Security for data isolation\n\n**Implementation**:\n- Supabase client for frontend authentication\n- Service role key for backend operations\n- RLS policies enforce user data access\n- JWT tokens for API authentication\n\n### Build System (2025-06-02)\n**Decision**: Vite for frontend, esbuild for backend bundling\n**Rationale**:\n- Fast development builds with Vite\n- Optimized production builds with code splitting\n- Single JavaScript bundle for easy deployment\n- TypeScript support throughout\n\n**Implementation**:\n- Vite handles React app with optimizations\n- esbuild bundles server code with minification\n- Separate build steps for frontend and backend\n- Production-ready asset optimization\n\n## 🔧 Technical Implementation Details\n\n### API Route Organization\n**Current Structure**:\n- `/api/health` - Health checks and monitoring\n- `/api/flashcards/*` - Flashcard CRUD operations\n- `/api/quizzes/*` - Quiz management and generation\n- `/api/documents/*` - Document processing and storage\n- `/api/ai/*` - AI integration endpoints\n\n**Design Principles**:\n- RESTful API design where applicable\n- Consistent error response format\n- Proper HTTP status codes\n- Input validation with Zod schemas\n\n### Environment Variable Strategy\n**Development vs Production**:\n- Development: Fallback values for quick setup\n- Production: Required environment variables with validation\n- Client variables prefixed with `VITE_`\n- Server variables without prefix\n\n**Security Considerations**:\n- No sensitive fallbacks in production\n- Startup validation for required variables\n- Clear separation of client/server configuration\n\n### Static File Serving Strategy\n**Caching Strategy**:\n- HTML files: No cache (always fresh)\n- JS/CSS asse ts: 1 year cache with immutable flag\n- Images and fonts: Standard caching\n- API responses: No cache by default\n\n**Security Headers**:\n- Content Security Policy for XSS protection\n- Frame options to prevent clickjacking\n- Content type sniffing prevention\n", "suffix": "- Referrer policy for privacy\n\n## 🚀 Deployment Decisions\n\n### Replit Configuration\n**Deployment Target**: Autoscale\n**Rationale**: \n- Automatic scaling based on traffic\n- Cost-effective for variable usage\n- Built-in load balancing\n- Easy environment management\n\n**Build Process**:\n- Single build command: `npm run build`\n- Health check endpoint: `/api/health`\n- Environment variables in deployment config\n- Automatic HTTPS and domain management\n\n### Production Optimizations\n**Frontend Optimizations**:\n- Code splitting by vendor and route\n- Asset hashing for cache busting\n- Tree shaking for smaller bundles\n- Lazy loading for non-critical components\n\n**Backend Optimizations**:\n- Minified server bundle\n- External package handling\n- Efficient static file serving\n- Proper error handling without stack traces\n\n## 📝 Development Workflow Decisions\n\n### Package Management\n**Decision**: Use npm as primary package manager\n**Rationale**:\n- Consistent with Node.js ecosystem\n- Good lock file support\n- Wide compatibility\n- Replit native support\n\n### TypeScript Configuration\n**Decision**: Strict TypeScript throughout the application\n**Rationale**:\n- Better developer experience\n- Catch errors at compile time\n- Improved code documentation\n- Better IDE support\n\n### Testing Strategy\n**Current Approach**: Build verification and manual testing\n**Future Considerations**:\n- Unit tests for critical business logic\n- Integration tests for API endpoints\n- End-to-end tests for user workflows\n- Performance testing for production loads\n\n## 🔄 Evolution and Future Considerations\n\n### Scalability Considerations\n- Database connection pooling for high traffic\n- CDN integration for static assets\n- Caching layer for frequently accessed data\n- Microservices migration if needed\n\n### Security Enhancements\n- Rate limiting for API endpoints\n- Advanced monitoring and alerting\n- Automated security scanning\n- Regular dependency updates\n\n### Feature Expansion\n- Real-time collaboration features\n- Advanced AI model support\n- Mobile application development\n- Offline functionality\n\n## 📊 Performance Benchmarks\n\n### Current Performance Targets\n- Page load time: < 2 seconds\n- API response time: < 500ms\n- Build time: < 2 minutes\n- Bundle size: < 1MB (gzipped)\n\n### Monitoring Metrics\n- Health check response time\n- Error rates by endpoint\n- User authentication success rate\n- AI provider integration reliability\n", "path": "docs/MEMORIES.md", "language": "markdown", "prefixBegin": 0, "suffixEnd": 0}}], ["28b3db42-4510-472c-87c7-f14f661183ce", {"value": {"selectedCode": "            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport {\n  Settings,\n  Shield,\n  Eye,\n  EyeOff,\n  <PERSON>rkles,\n  Z<PERSON>,\n  Brain,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash and generation model to Pro Preview\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = (values: AIConfigFormValues) => {\n    // Set the legacy 'model' field to match generationModel for backward compatibility\n    const settingsToSave = {\n      ...values,\n      model: values.generationModel,\n    };\n\n    setAIProviderSettings(settingsToSave);\n    toast({\n      title: \"AI Provider Configured\",\n      description: \"Your AI settings have been saved successfully.\",\n    });\n    navigate(\"/\");\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash for extraction and Pro Preview for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n", "suffix": "\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is used securely and never stored\n                      permanently.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is sent securely to our backend and used only\n                    for your current session. We don't store your credentials\n                    permanently. All AI requests are processed through your\n                    configured provider using your own API key, so you maintain\n                    control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                Save Configuration\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["f26ae645-5cb3-4a1e-a5d2-51af3cc81f57", {"value": {"selectedCode": "        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport {\n  Settings,\n  Shield,\n  Eye,\n  EyeOff,\n  <PERSON>rkles,\n  Z<PERSON>,\n  Brain,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash and generation model to Pro Preview\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash\")?.id ||\n        models[0].id;\n      const generationModel =\n", "suffix": "\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = (values: AIConfigFormValues) => {\n    // Set the legacy 'model' field to match generationModel for backward compatibility\n    const settingsToSave = {\n      ...values,\n      model: values.generationModel,\n    };\n\n    setAIProviderSettings(settingsToSave);\n    toast({\n      title: \"AI Provider Configured\",\n      description: \"Your AI settings have been saved successfully.\",\n    });\n    navigate(\"/\");\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash for extraction and Pro Preview for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is used securely and never stored\n                      permanently.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is sent securely to our backend and used only\n                    for your current session. We don't store your credentials\n                    permanently. All AI requests are processed through your\n                    configured provider using your own API key, so you maintain\n                    control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                Save Configuration\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["49b5b531-cda7-44c1-9bf8-60596b1c8306", {"value": {"selectedCode": "        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport {\n  Settings,\n  Shield,\n  Eye,\n  EyeOff,\n  <PERSON>rkles,\n  Z<PERSON>,\n  Brain,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash and generation model to Pro Preview\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash\")?.id ||\n        models[0].id;\n      const generationModel =\n", "suffix": "\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = (values: AIConfigFormValues) => {\n    // Set the legacy 'model' field to match generationModel for backward compatibility\n    const settingsToSave = {\n      ...values,\n      model: values.generationModel,\n    };\n\n    setAIProviderSettings(settingsToSave);\n    toast({\n      title: \"AI Provider Configured\",\n      description: \"Your AI settings have been saved successfully.\",\n    });\n    navigate(\"/\");\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash for extraction and Pro Preview for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is used securely and never stored\n                      permanently.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is sent securely to our backend and used only\n                    for your current session. We don't store your credentials\n                    permanently. All AI requests are processed through your\n                    configured provider using your own API key, so you maintain\n                    control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                Save Configuration\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["de011ae7-a2e3-4107-ab9e-205679299d58", {"value": {"selectedCode": "", "prefix": "import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\n", "suffix": "import { QuizSettings } from '@/types/quiz-settings';\nimport { getQuizSettings, setQuizSettings } from '@/lib/quiz-settings';\n\ninterface QuizSettingsContextType {\n  settings: QuizSettings;\n  updateSetting: <K extends keyof QuizSettings>(key: K, value: QuizSettings[K]) => void;\n  resetSettings: () => void;\n}\n\nconst QuizSettingsContext = createContext<QuizSettingsContextType | undefined>(undefined);\n\ninterface QuizSettingsProviderProps {\n  children: ReactNode;\n}\n\nexport const QuizSettingsProvider: React.FC<QuizSettingsProviderProps> = ({ children }) => {\n  const [settings, setSettingsState] = useState<QuizSettings>(getQuizSettings);\n\n  const updateSetting = <K extends keyof QuizSettings>(key: K, value: QuizSettings[K]) => {\n    const newSettings = { ...settings, [key]: value };\n    setSettingsState(newSettings);\n    setQuizSettings(newSettings);\n  };\n\n  const resetSettings = () => {\n    const defaultSettings = getQuizSettings();\n    setSettingsState(defaultSettings);\n    setQuizSettings(defaultSettings);\n  };\n\n  useEffect(() => {\n    // Sync settings on mount\n    const currentSettings = getQuizSettings();\n    setSettingsState(currentSettings);\n  }, []);\n\n  return (\n    <QuizSettingsContext.Provider value={{ settings, updateSetting, resetSettings }}>\n      {children}\n    </QuizSettingsContext.Provider>\n  );\n};\n\nexport const useQuizSettings = (): QuizSettingsContextType => {\n  const context = useContext(QuizSettingsContext);\n  if (context === undefined) {\n    throw new Error('useQuizSettings must be used within a QuizSettingsProvider');\n  }\n  return context;\n};\n", "path": "client/src/contexts/QuizSettingsContext.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["ff9f05a3-c8f1-46ef-b383-76b8aa78918a", {"value": {"selectedCode": "          name: \"Google Gemini Pro 1.5 (Recommended)\",", "prefix": "import { AIProviderSettings } from \"@/types\";\n\n/**\n * Set AI provider settings (excluding API key which is stored securely on server)\n */\nexport function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {\n  // Store only non-sensitive configuration in localStorage\n  const safeSettings = {\n    provider: settings.provider,\n    baseUrl: settings.baseUrl,\n    extractionModel: settings.extractionModel,\n    generationModel: settings.generationModel,\n    model: settings.generationModel, // Keep model field aligned with generationModel\n    // Never store apiKey in localStorage\n  };\n  localStorage.setItem(\"aiProviderSettings\", JSON.stringify(safeSettings));\n}\n\n/**\n * Get AI provider settings from localStorage\n */\nexport function getAIProviderSettings(): AIProviderSettings {\n  const systemDefaultProvider = \"OpenRouter\";\n  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);\n  const systemDefaultModels = getAvailableModels(systemDefaultProvider);\n\n  const defaultExtractionModelId = \"google/gemini-flash-1.5\";\n  const defaultGenerationModelId = \"google/gemini-pro-1.5\";\n\n  const systemDefaultExtractionModel =\n    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n  const systemDefaultGenerationModel =\n    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n\n  const systemDefaults: AIProviderSettings = {\n    provider: systemDefaultProvider,\n    baseUrl: systemDefaultBaseUrl,\n    apiKey: \"\", // Never use environment variables for API keys on client-side\n    extractionModel: systemDefaultExtractionModel,\n    generationModel: systemDefaultGenerationModel,\n    model: systemDefaultGenerationModel, // Legacy field, matches generationModel\n  };\n\n  const storedSettings = localStorage.getItem(\"aiProviderSettings\");\n  if (!storedSettings) {\n    return systemDefaults;\n  }\n\n  try {\n    const parsedSettings = JSON.parse(\n      storedSettings\n    ) as Partial<AIProviderSettings>;\n\n    const currentProvider = parsedSettings.provider || systemDefaults.provider;\n    const modelsForCurrentProvider = getAvailableModels(currentProvider);\n\n    let finalExtractionModel = parsedSettings.extractionModel;\n    if (\n      !finalExtractionModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)\n    ) {\n      finalExtractionModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.extractionModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    let finalGenerationModel = parsedSettings.generationModel;\n    if (\n      !finalGenerationModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)\n    ) {\n      finalGenerationModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.generationModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    return {\n      provider: currentProvider,\n      baseUrl:\n        parsedSettings.baseUrl ||\n        getDefaultBaseUrl(currentProvider) ||\n        systemDefaults.baseUrl,\n      apiKey: \"\", // API keys are never stored in localStorage - retrieved from server when needed\n      extractionModel: finalExtractionModel,\n      generationModel: finalGenerationModel,\n      model: finalGenerationModel, // Legacy field always matches generationModel\n    };\n  } catch (e) {\n    // console.error(\"Failed to parse AI provider settings, returning system defaults:\", e);\n    return systemDefaults;\n  }\n}\n\n/**\n * Check if AI provider settings are configured\n */\nexport function isAIProviderConfigured(): boolean {\n  const settings = getAIProviderSettings();\n  return Boolean(settings.apiKey.trim());\n}\n\n/**\n * Get list of available AI models by provider\n */\nexport function getAvailableModels(\n  provider: string\n): { id: string; name: string }[] {\n  switch (provider) {\n    case \"OpenRouter\":\n      return [\n        { id: \"google/gemini-flash-1.5\", name: \"Google Gemini Flash 1.5\" },\n        {\n          id: \"google/gemini-pro-1.5\",\n", "suffix": "\n        },\n      ];\n    default:\n      return [];\n  }\n}\n\n/**\n * Get available AI providers\n */\nexport function getAvailableProviders(): string[] {\n  return [\"OpenRouter\"];\n}\n\n/**\n * Get default base URL for a provider\n */\nexport function getDefaultBaseUrl(provider: string): string {\n  switch (provider) {\n    case \"OpenRouter\":\n      return \"https://openrouter.ai/api/v1\";\n    default:\n      return \"\";\n  }\n}\n\n/**\n * Get recommended model for specific tasks\n * This follows the PRD recommendations\n */\nexport function getRecommendedModelForTask(\n  task: \"extraction\" | \"generation\"\n): string {\n  const settings = getAIProviderSettings();\n  switch (task) {\n    case \"extraction\":\n      return settings.extractionModel || \"google/gemini-flash-1.5\";\n    case \"generation\":\n      return settings.generationModel || \"google/gemini-pro-1.5\";\n    default:\n      return settings.generationModel || \"google/gemini-pro-1.5\";\n  }\n}\n", "path": "client/src/lib/ai-provider.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["180f323b-9329-422b-83f3-3c7bb18b1967", {"value": {"selectedCode": "", "prefix": "import { AIProviderSettings } from \"@/types\";\n\n/**\n * Set AI provider settings (excluding API key which is stored securely on server)\n */\nexport function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {\n  // Store only non-sensitive configuration in localStorage\n  const safeSettings = {\n    provider: settings.provider,\n    baseUrl: settings.baseUrl,\n    extractionModel: settings.extractionModel,\n    generationModel: settings.generationModel,\n    model: settings.generationModel, // Keep model field aligned with generationModel\n    // Never store apiKey in localStorage\n  };\n  localStorage.setItem(\"aiProviderSettings\", JSON.stringify(safeSettings));\n}\n\n/**\n * Get AI provider settings from localStorage\n */\nexport function getAIProviderSettings(): AIProviderSettings {\n  const systemDefaultProvider = \"OpenRouter\";\n  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);\n  const systemDefaultModels = getAvailableModels(systemDefaultProvider);\n\n  const defaultExtractionModelId = \"google/gemini-flash-1.5\";\n  const defaultGenerationModelId = \"google/gemini-pro-1.5\";\n\n  const systemDefaultExtractionModel =\n    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n  const systemDefaultGenerationModel =\n    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n\n  const systemDefaults: AIProviderSettings = {\n    provider: systemDefaultProvider,\n    baseUrl: systemDefaultBaseUrl,\n    apiKey: \"\", // Never use environment variables for API keys on client-side\n    extractionModel: systemDefaultExtractionModel,\n    generationModel: systemDefaultGenerationModel,\n    model: systemDefaultGenerationModel, // Legacy field, matches generationModel\n  };\n\n  const storedSettings = localStorage.getItem(\"aiProviderSettings\");\n  if (!storedSettings) {\n    return systemDefaults;\n  }\n\n  try {\n    const parsedSettings = JSON.parse(\n      storedSettings\n    ) as Partial<AIProviderSettings>;\n\n    const currentProvider = parsedSettings.provider || systemDefaults.provider;\n    const modelsForCurrentProvider = getAvailableModels(currentProvider);\n\n    let finalExtractionModel = parsedSettings.extractionModel;\n    if (\n      !finalExtractionModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)\n    ) {\n      finalExtractionModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.extractionModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    let finalGenerationModel = parsedSettings.generationModel;\n    if (\n      !finalGenerationModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)\n    ) {\n      finalGenerationModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.generationModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    return {\n      provider: currentProvider,\n      baseUrl:\n        parsedSettings.baseUrl ||\n        getDefaultBaseUrl(currentProvider) ||\n        systemDefaults.baseUrl,\n      apiKey: \"\", // API keys are never stored in localStorage - retrieved from server when needed\n      extractionModel: finalExtractionModel,\n      generationModel: finalGenerationModel,\n      model: finalGenerationModel, // Legacy field always matches generationModel\n    };\n  } catch (e) {\n    // console.error(\"Failed to parse AI provider settings, returning system defaults:\", e);\n    return systemDefaults;\n  }\n}\n\n/**\n * Check if AI provider settings are configured\n */\nexport function isAIProviderConfigured(): boolean {\n  const settings = getAIProviderSettings();\n  return Boolean(settings.apiKey.trim());\n}\n\n/**\n * Get list of available AI models by provider\n */\nexport function getAvailableModels(\n  provider: string\n): { id: string; name: string }[] {\n  switch (provider) {\n    case \"OpenRouter\":\n", "suffix": "      return [\n        { id: \"google/gemini-flash-1.5\", name: \"Google Gemini Flash 1.5\" },\n        {\n          id: \"google/gemini-pro-1.5\",\n          name: \"Google Gemini Pro 1.5 (Recommended)\",\n        },\n      ];\n    default:\n      return [];\n  }\n}\n\n/**\n * Get available AI providers\n */\nexport function getAvailableProviders(): string[] {\n  return [\"OpenRouter\"];\n}\n\n/**\n * Get default base URL for a provider\n */\nexport function getDefaultBaseUrl(provider: string): string {\n  switch (provider) {\n    case \"OpenRouter\":\n      return \"https://openrouter.ai/api/v1\";\n    default:\n      return \"\";\n  }\n}\n\n/**\n * Get recommended model for specific tasks\n * This follows the PRD recommendations\n */\nexport function getRecommendedModelForTask(\n  task: \"extraction\" | \"generation\"\n): string {\n  const settings = getAIProviderSettings();\n  switch (task) {\n    case \"extraction\":\n      return settings.extractionModel || \"google/gemini-flash-1.5\";\n    case \"generation\":\n      return settings.generationModel || \"google/gemini-pro-1.5\";\n    default:\n      return settings.generationModel || \"google/gemini-pro-1.5\";\n  }\n}\n", "path": "client/src/lib/ai-provider.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["de5ed95f-903c-4f0f-a1a8-4a61110479c9", {"value": {"selectedCode": "        { id: \"google/gemini-flash-1.5\", name: \"Google Gemini Flash 1.5\" },", "prefix": "import { AIProviderSettings } from \"@/types\";\n\n/**\n * Set AI provider settings (excluding API key which is stored securely on server)\n */\nexport function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {\n  // Store only non-sensitive configuration in localStorage\n  const safeSettings = {\n    provider: settings.provider,\n    baseUrl: settings.baseUrl,\n    extractionModel: settings.extractionModel,\n    generationModel: settings.generationModel,\n    model: settings.generationModel, // Keep model field aligned with generationModel\n    // Never store apiKey in localStorage\n  };\n  localStorage.setItem(\"aiProviderSettings\", JSON.stringify(safeSettings));\n}\n\n/**\n * Get AI provider settings from localStorage\n */\nexport function getAIProviderSettings(): AIProviderSettings {\n  const systemDefaultProvider = \"OpenRouter\";\n  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);\n  const systemDefaultModels = getAvailableModels(systemDefaultProvider);\n\n  const defaultExtractionModelId = \"google/gemini-flash-1.5\";\n  const defaultGenerationModelId = \"google/gemini-pro-1.5\";\n\n  const systemDefaultExtractionModel =\n    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n  const systemDefaultGenerationModel =\n    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n\n  const systemDefaults: AIProviderSettings = {\n    provider: systemDefaultProvider,\n    baseUrl: systemDefaultBaseUrl,\n    apiKey: \"\", // Never use environment variables for API keys on client-side\n    extractionModel: systemDefaultExtractionModel,\n    generationModel: systemDefaultGenerationModel,\n    model: systemDefaultGenerationModel, // Legacy field, matches generationModel\n  };\n\n  const storedSettings = localStorage.getItem(\"aiProviderSettings\");\n  if (!storedSettings) {\n    return systemDefaults;\n  }\n\n  try {\n    const parsedSettings = JSON.parse(\n      storedSettings\n    ) as Partial<AIProviderSettings>;\n\n    const currentProvider = parsedSettings.provider || systemDefaults.provider;\n    const modelsForCurrentProvider = getAvailableModels(currentProvider);\n\n    let finalExtractionModel = parsedSettings.extractionModel;\n    if (\n      !finalExtractionModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)\n    ) {\n      finalExtractionModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.extractionModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    let finalGenerationModel = parsedSettings.generationModel;\n    if (\n      !finalGenerationModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)\n    ) {\n      finalGenerationModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.generationModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    return {\n      provider: currentProvider,\n      baseUrl:\n        parsedSettings.baseUrl ||\n        getDefaultBaseUrl(currentProvider) ||\n        systemDefaults.baseUrl,\n      apiKey: \"\", // API keys are never stored in localStorage - retrieved from server when needed\n      extractionModel: finalExtractionModel,\n      generationModel: finalGenerationModel,\n      model: finalGenerationModel, // Legacy field always matches generationModel\n    };\n  } catch (e) {\n    // console.error(\"Failed to parse AI provider settings, returning system defaults:\", e);\n    return systemDefaults;\n  }\n}\n\n/**\n * Check if AI provider settings are configured\n */\nexport function isAIProviderConfigured(): boolean {\n  const settings = getAIProviderSettings();\n  return Boolean(settings.apiKey.trim());\n}\n\n/**\n * Get list of available AI models by provider\n */\nexport function getAvailableModels(\n  provider: string\n): { id: string; name: string }[] {\n  switch (provider) {\n    case \"OpenRouter\":\n      return [\n", "suffix": "\n        {\n          id: \"google/gemini-pro-1.5\",\n          name: \"Google Gemini Pro 1.5 (Recommended)\",\n        },\n      ];\n    default:\n      return [];\n  }\n}\n\n/**\n * Get available AI providers\n */\nexport function getAvailableProviders(): string[] {\n  return [\"OpenRouter\"];\n}\n\n/**\n * Get default base URL for a provider\n */\nexport function getDefaultBaseUrl(provider: string): string {\n  switch (provider) {\n    case \"OpenRouter\":\n      return \"https://openrouter.ai/api/v1\";\n    default:\n      return \"\";\n  }\n}\n\n/**\n * Get recommended model for specific tasks\n * This follows the PRD recommendations\n */\nexport function getRecommendedModelForTask(\n  task: \"extraction\" | \"generation\"\n): string {\n  const settings = getAIProviderSettings();\n  switch (task) {\n    case \"extraction\":\n      return settings.extractionModel || \"google/gemini-flash-1.5\";\n    case \"generation\":\n      return settings.generationModel || \"google/gemini-pro-1.5\";\n    default:\n      return settings.generationModel || \"google/gemini-pro-1.5\";\n  }\n}\n", "path": "client/src/lib/ai-provider.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["b6280b52-ebb8-4130-8822-1d9a6c2c33ce", {"value": {"selectedCode": "DEFAULT_GENERATION_MODEL=google/gemini-2.5-pro-preview", "prefix": "# =============================================================================\n# ChewyAI Environment Configuration\n# =============================================================================\n# Copy this file to .env and fill in your actual values\n# NEVER commit .env files to version control\n\n# =============================================================================\n# CLIENT ENVIRONMENT VARIABLES (VITE_ prefix for Vite)\n# =============================================================================\n# These are exposed to the browser - only include non-sensitive values\n\n# Supabase Configuration (Client)\nVITE_SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co\nVITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.0qS0Nh1aV4GjZYniANoUZKDDcuCcQxzTm-DnTeZsNlQ\n\n# API Configuration\nVITE_API_BASE_URL=http://localhost:5000/api\n\n# =============================================================================\n# SERVER ENVIRONMENT VARIABLES\n# =============================================================================\n# These are only available on the server - include sensitive values here\n\n# Application Configuration\nNODE_ENV=development\nPORT=5000\n\n# Supabase Configuration (Server)\nSUPABASE_URL=your-supabase-url-here\nSUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key-here\n\n# Database Configuration (if using direct database access)\nVITE_DATABASE_PASSWORD=your-database-password-here\n\n# =============================================================================\n# AI PROVIDER CONFIGURATION (Optional)\n# =============================================================================\n# Default AI provider settings - users will configure their own in the app\n\nDEFAULT_AI_PROVIDER=openrouter\nDEFAULT_AI_BASE_URL=https://openrouter.ai/api/v1\nDEFAULT_EXTRACTION_MODEL=google/gemini-2.5-flash-preview-05-20\n", "suffix": "\n\n# =============================================================================\n# SECURITY CONFIGURATION\n# =============================================================================\n# Generate secure random strings for production\n\nJWT_SECRET=your-jwt-secret-change-this-in-production\nSESSION_SECRET=your-session-secret-change-this-in-production\n\n# =============================================================================\n# CORS CONFIGURATION\n# =============================================================================\n# Frontend URL for CORS configuration\n\nFRONTEND_URL=http://localhost:3000\n\n# =============================================================================\n# ADDITIONAL DATABASE CONFIGURATION (Optional)\n# =============================================================================\n# For local SQLite storage if needed\n\nDATABASE_URL=./data/chewyai.sqlite\n\n# =============================================================================\n# PRODUCTION DEPLOYMENT NOTES\n# =============================================================================\n# For Replit deployment, set these in Replit Secrets:\n# - SUPABASE_URL\n# - SUPABASE_SERVICE_ROLE_KEY\n# - VITE_SUPABASE_URL\n# - VITE_SUPABASE_ANON_KEY\n# - NODE_ENV=production\n# - PORT=80\n#\n# The VITE_ variables will be automatically included in the client build\n# The server variables will only be available to the backend", "path": ".env.example", "language": "shellscript", "prefixBegin": 0, "suffixEnd": 60}}], ["1dc2d288-2af1-4ca8-92a8-09bf937370b1", {"value": {"selectedCode": "", "prefix": "import { AIProviderSettings } from \"@/types\";\n\n/**\n * Set AI provider settings (excluding API key which is stored securely on server)\n */\nexport function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {\n  // Store only non-sensitive configuration in localStorage\n  const safeSettings = {\n    provider: settings.provider,\n    baseUrl: settings.baseUrl,\n    extractionModel: settings.extractionModel,\n    generationModel: settings.generationModel,\n    model: settings.generationModel, // Keep model field aligned with generationModel\n    // Never store apiKey in localStorage\n  };\n  localStorage.setItem(\"aiProviderSettings\", JSON.stringify(safeSettings));\n}\n\n/**\n * Get AI provider settings from localStorage\n */\nexport function getAIProviderSettings(): AIProviderSettings {\n  const systemDefaultProvider = \"OpenRouter\";\n  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);\n  const systemDefaultModels = getAvailableModels(systemDefaultProvider);\n\n  const defaultExtractionModelId = \"google/gemini-flash-1.5\";\n  const defaultGenerationModelId = \"google/gemini-pro-1.5\";\n\n  const systemDefaultExtractionModel =\n    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n  const systemDefaultGenerationModel =\n    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n\n  const systemDefaults: AIProviderSettings = {\n    provider: systemDefaultProvider,\n    baseUrl: systemDefaultBaseUrl,\n    apiKey: \"\", // Never use environment variables for API keys on client-side\n    extractionModel: systemDefaultExtractionModel,\n    generationModel: systemDefaultGenerationModel,\n    model: systemDefaultGenerationModel, // Legacy field, matches generationModel\n  };\n\n  const storedSettings = localStorage.getItem(\"aiProviderSettings\");\n  if (!storedSettings) {\n    return systemDefaults;\n  }\n\n  try {\n    const parsedSettings = JSON.parse(\n      storedSettings\n    ) as Partial<AIProviderSettings>;\n\n    const currentProvider = parsedSettings.provider || systemDefaults.provider;\n    const modelsForCurrentProvider = getAvailableModels(currentProvider);\n\n    let finalExtractionModel = parsedSettings.extractionModel;\n    if (\n      !finalExtractionModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)\n    ) {\n      finalExtractionModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.extractionModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    let finalGenerationModel = parsedSettings.generationModel;\n    if (\n      !finalGenerationModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)\n    ) {\n      finalGenerationModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.generationModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    return {\n      provider: currentProvider,\n      baseUrl:\n        parsedSettings.baseUrl ||\n        getDefaultBaseUrl(currentProvider) ||\n        systemDefaults.baseUrl,\n      apiKey: \"\", // API keys are never stored in localStorage - retrieved from server when needed\n      extractionModel: finalExtractionModel,\n      generationModel: finalGenerationModel,\n      model: finalGenerationModel, // Legacy field always matches generationModel\n    };\n  } catch (e) {\n    // console.error(\"Failed to parse AI provider settings, returning system defaults:\", e);\n    return systemDefaults;\n  }\n}\n\n/**\n * Check if AI provider settings are configured\n */\nexport function isAIProviderConfigured(): boolean {\n  const settings = getAIProviderSettings();\n  return Boolean(settings.apiKey.trim());\n}\n\n/**\n * Get list of available AI models by provider\n */\nexport function getAvailableModels(\n  provider: string\n): { id: string; name: string }[] {\n  switch (provider) {\n    case \"OpenRouter\":\n      return [\n        { id: \"google/gemini-2.5-flash-preview-05-20\", name: \"Google Gemini Flash 1.5\" },\n        {\n          id: \"google/gemini-2.5-pro-preview\",\n          name: \"Google Gemini Pro 1.5 (Recommended)\",\n        },\n      ];\n    default:\n      return [];\n  }\n}\n\n/**\n * Get available AI providers\n */\nexport function getAvailableProviders(): string[] {\n  return [\"OpenRouter\"];\n}\n\n/**\n * Get default base URL for a provider\n */\nexport function getDefaultBaseUrl(provider: string): string {\n  switch (provider) {\n    case \"OpenRouter\":\n      return \"https://openrouter.ai/api/v1\";\n    default:\n      return \"\";\n  }\n}\n\n/**\n * Get recommended model for specific tasks\n * This follows the PRD recommendations\n */\nexport function getRecommendedModelForTask(\n  task: \"extraction\" | \"generation\"\n): string {\n  const settings = getAIProviderSettings();\n  switch (task) {\n    case \"extraction\":\n", "suffix": "      return settings.extractionModel || \"google/gemini-flash-1.5\";\n    case \"generation\":\n      return settings.generationModel || \"google/gemini-pro-1.5\";\n    default:\n      return settings.generationModel || \"google/gemini-pro-1.5\";\n  }\n}\n", "path": "client/src/lib/ai-provider.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["47862ace-1abd-4093-9136-6789bdde0009", {"value": {"selectedCode": "", "prefix": "import { AIProviderSettings } from \"@/types\";\n\n/**\n * Set AI provider settings (excluding API key which is stored securely on server)\n */\nexport function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {\n  // Store only non-sensitive configuration in localStorage\n  const safeSettings = {\n    provider: settings.provider,\n    baseUrl: settings.baseUrl,\n    extractionModel: settings.extractionModel,\n    generationModel: settings.generationModel,\n    model: settings.generationModel, // Keep model field aligned with generationModel\n    // Never store apiKey in localStorage\n  };\n  localStorage.setItem(\"aiProviderSettings\", JSON.stringify(safeSettings));\n}\n\n/**\n * Get AI provider settings from localStorage\n */\nexport function getAIProviderSettings(): AIProviderSettings {\n  const systemDefaultProvider = \"OpenRouter\";\n  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);\n  const systemDefaultModels = getAvailableModels(systemDefaultProvider);\n\n  const defaultExtractionModelId = \"google/gemini-flash-1.5\";\n  const defaultGenerationModelId = \"google/gemini-pro-1.5\";\n\n  const systemDefaultExtractionModel =\n    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n  const systemDefaultGenerationModel =\n    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n\n  const systemDefaults: AIProviderSettings = {\n    provider: systemDefaultProvider,\n    baseUrl: systemDefaultBaseUrl,\n    apiKey: \"\", // Never use environment variables for API keys on client-side\n    extractionModel: systemDefaultExtractionModel,\n    generationModel: systemDefaultGenerationModel,\n    model: systemDefaultGenerationModel, // Legacy field, matches generationModel\n  };\n\n  const storedSettings = localStorage.getItem(\"aiProviderSettings\");\n  if (!storedSettings) {\n    return systemDefaults;\n  }\n\n  try {\n    const parsedSettings = JSON.parse(\n      storedSettings\n    ) as Partial<AIProviderSettings>;\n\n    const currentProvider = parsedSettings.provider || systemDefaults.provider;\n    const modelsForCurrentProvider = getAvailableModels(currentProvider);\n\n    let finalExtractionModel = parsedSettings.extractionModel;\n    if (\n      !finalExtractionModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)\n    ) {\n      finalExtractionModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.extractionModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    let finalGenerationModel = parsedSettings.generationModel;\n    if (\n      !finalGenerationModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)\n    ) {\n      finalGenerationModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.generationModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    return {\n      provider: currentProvider,\n      baseUrl:\n        parsedSettings.baseUrl ||\n        getDefaultBaseUrl(currentProvider) ||\n        systemDefaults.baseUrl,\n      apiKey: \"\", // API keys are never stored in localStorage - retrieved from server when needed\n      extractionModel: finalExtractionModel,\n      generationModel: finalGenerationModel,\n      model: finalGenerationModel, // Legacy field always matches generationModel\n    };\n  } catch (e) {\n    // console.error(\"Failed to parse AI provider settings, returning system defaults:\", e);\n    return systemDefaults;\n  }\n}\n\n/**\n * Check if AI provider settings are configured\n */\nexport function isAIProviderConfigured(): boolean {\n  const settings = getAIProviderSettings();\n  return Boolean(settings.apiKey.trim());\n}\n\n/**\n * Get list of available AI models by provider\n */\nexport function getAvailableModels(\n  provider: string\n): { id: string; name: string }[] {\n  switch (provider) {\n    case \"OpenRouter\":\n      return [\n        { id: \"google/gemini-2.5-flash-preview-05-20\", name: \"Google Gemini Flash 1.5\" },\n", "suffix": "        {\n          id: \"google/gemini-2.5-pro-preview\",\n          name: \"Google Gemini Pro 1.5 (Recommended)\",\n        },\n      ];\n    default:\n      return [];\n  }\n}\n\n/**\n * Get available AI providers\n */\nexport function getAvailableProviders(): string[] {\n  return [\"OpenRouter\"];\n}\n\n/**\n * Get default base URL for a provider\n */\nexport function getDefaultBaseUrl(provider: string): string {\n  switch (provider) {\n    case \"OpenRouter\":\n      return \"https://openrouter.ai/api/v1\";\n    default:\n      return \"\";\n  }\n}\n\n/**\n * Get recommended model for specific tasks\n * This follows the PRD recommendations\n */\nexport function getRecommendedModelForTask(\n  task: \"extraction\" | \"generation\"\n): string {\n  const settings = getAIProviderSettings();\n  switch (task) {\n    case \"extraction\":\n      return settings.extractionModel || \"google/gemini-2.5-flash-preview-05-20\";\n    case \"generation\":\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n    default:\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n  }\n}\n", "path": "client/src/lib/ai-provider.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["23eec07c-4f6a-42e4-bdb8-e6c78eeff2c3", {"value": {"selectedCode": "  const defaultExtractionModelId = \"google/gemini-2.5-flash-preview-05-20\";", "prefix": "import { AIProviderSettings } from \"@/types\";\n\n/**\n * Set AI provider settings (excluding API key which is stored securely on server)\n */\nexport function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {\n  // Store only non-sensitive configuration in localStorage\n  const safeSettings = {\n    provider: settings.provider,\n    baseUrl: settings.baseUrl,\n    extractionModel: settings.extractionModel,\n    generationModel: settings.generationModel,\n    model: settings.generationModel, // Keep model field aligned with generationModel\n    // Never store apiKey in localStorage\n  };\n  localStorage.setItem(\"aiProviderSettings\", JSON.stringify(safeSettings));\n}\n\n/**\n * Get AI provider settings from localStorage\n */\nexport function getAIProviderSettings(): AIProviderSettings {\n  const systemDefaultProvider = \"OpenRouter\";\n  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);\n  const systemDefaultModels = getAvailableModels(systemDefaultProvider);\n\n", "suffix": "\n  const defaultGenerationModelId = \"google/gemini-2.5-pro-preview\";\n\n  const systemDefaultExtractionModel =\n    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n  const systemDefaultGenerationModel =\n    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n\n  const systemDefaults: AIProviderSettings = {\n    provider: systemDefaultProvider,\n    baseUrl: systemDefaultBaseUrl,\n    apiKey: \"\", // Never use environment variables for API keys on client-side\n    extractionModel: systemDefaultExtractionModel,\n    generationModel: systemDefaultGenerationModel,\n    model: systemDefaultGenerationModel, // Legacy field, matches generationModel\n  };\n\n  const storedSettings = localStorage.getItem(\"aiProviderSettings\");\n  if (!storedSettings) {\n    return systemDefaults;\n  }\n\n  try {\n    const parsedSettings = JSON.parse(\n      storedSettings\n    ) as Partial<AIProviderSettings>;\n\n    const currentProvider = parsedSettings.provider || systemDefaults.provider;\n    const modelsForCurrentProvider = getAvailableModels(currentProvider);\n\n    let finalExtractionModel = parsedSettings.extractionModel;\n    if (\n      !finalExtractionModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)\n    ) {\n      finalExtractionModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.extractionModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    let finalGenerationModel = parsedSettings.generationModel;\n    if (\n      !finalGenerationModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)\n    ) {\n      finalGenerationModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.generationModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    return {\n      provider: currentProvider,\n      baseUrl:\n        parsedSettings.baseUrl ||\n        getDefaultBaseUrl(currentProvider) ||\n        systemDefaults.baseUrl,\n      apiKey: \"\", // API keys are never stored in localStorage - retrieved from server when needed\n      extractionModel: finalExtractionModel,\n      generationModel: finalGenerationModel,\n      model: finalGenerationModel, // Legacy field always matches generationModel\n    };\n  } catch (e) {\n    // console.error(\"Failed to parse AI provider settings, returning system defaults:\", e);\n    return systemDefaults;\n  }\n}\n\n/**\n * Check if AI provider settings are configured\n */\nexport function isAIProviderConfigured(): boolean {\n  const settings = getAIProviderSettings();\n  return Boolean(settings.apiKey.trim());\n}\n\n/**\n * Get list of available AI models by provider\n */\nexport function getAvailableModels(\n  provider: string\n): { id: string; name: string }[] {\n  switch (provider) {\n    case \"OpenRouter\":\n      return [\n        { id: \"google/gemini-2.5-flash-preview-05-20\", name: \"Google Gemini Flash 1.5\" },\n        {\n          id: \"google/gemini-2.5-pro-preview\",\n          name: \"Google Gemini Pro 1.5 (Recommended)\",\n        },\n      ];\n    default:\n      return [];\n  }\n}\n\n/**\n * Get available AI providers\n */\nexport function getAvailableProviders(): string[] {\n  return [\"OpenRouter\"];\n}\n\n/**\n * Get default base URL for a provider\n */\nexport function getDefaultBaseUrl(provider: string): string {\n  switch (provider) {\n    case \"OpenRouter\":\n      return \"https://openrouter.ai/api/v1\";\n    default:\n      return \"\";\n  }\n}\n\n/**\n * Get recommended model for specific tasks\n * This follows the PRD recommendations\n */\nexport function getRecommendedModelForTask(\n  task: \"extraction\" | \"generation\"\n): string {\n  const settings = getAIProviderSettings();\n  switch (task) {\n    case \"extraction\":\n      return settings.extractionModel || \"google/gemini-2.5-flash-preview-05-20\";\n    case \"generation\":\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n    default:\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n  }\n}\n", "path": "client/src/lib/ai-provider.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["0d7bf0bf-3e3e-4671-b8a7-ba3e6fdefcde", {"value": {"selectedCode": "", "prefix": "import { AIProviderSettings } from \"@/types\";\n\n/**\n * Set AI provider settings (excluding API key which is stored securely on server)\n */\nexport function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {\n  // Store only non-sensitive configuration in localStorage\n  const safeSettings = {\n    provider: settings.provider,\n    baseUrl: settings.baseUrl,\n    extractionModel: settings.extractionModel,\n    generationModel: settings.generationModel,\n    model: settings.generationModel, // Keep model field aligned with generationModel\n    // Never store apiKey in localStorage\n  };\n  localStorage.setItem(\"aiProviderSettings\", JSON.stringify(safeSettings));\n}\n\n/**\n * Get AI provider settings from localStorage\n */\nexport function getAIProviderSettings(): AIProviderSettings {\n  const systemDefaultProvider = \"OpenRouter\";\n  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);\n  const systemDefaultModels = getAvailableModels(systemDefaultProvider);\n\n  const defaultExtractionModelId = \"google/gemini-2.5-flash-preview-05-20\";\n  const defaultGenerationModelId = \"google/gemini-2.5-pro-preview\";\n\n", "suffix": "  const systemDefaultExtractionModel =\n    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n  const systemDefaultGenerationModel =\n    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n\n  const systemDefaults: AIProviderSettings = {\n    provider: systemDefaultProvider,\n    baseUrl: systemDefaultBaseUrl,\n    apiKey: \"\", // Never use environment variables for API keys on client-side\n    extractionModel: systemDefaultExtractionModel,\n    generationModel: systemDefaultGenerationModel,\n    model: systemDefaultGenerationModel, // Legacy field, matches generationModel\n  };\n\n  const storedSettings = localStorage.getItem(\"aiProviderSettings\");\n  if (!storedSettings) {\n    return systemDefaults;\n  }\n\n  try {\n    const parsedSettings = JSON.parse(\n      storedSettings\n    ) as Partial<AIProviderSettings>;\n\n    const currentProvider = parsedSettings.provider || systemDefaults.provider;\n    const modelsForCurrentProvider = getAvailableModels(currentProvider);\n\n    let finalExtractionModel = parsedSettings.extractionModel;\n    if (\n      !finalExtractionModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)\n    ) {\n      finalExtractionModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.extractionModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    let finalGenerationModel = parsedSettings.generationModel;\n    if (\n      !finalGenerationModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)\n    ) {\n      finalGenerationModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.generationModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    return {\n      provider: currentProvider,\n      baseUrl:\n        parsedSettings.baseUrl ||\n        getDefaultBaseUrl(currentProvider) ||\n        systemDefaults.baseUrl,\n      apiKey: \"\", // API keys are never stored in localStorage - retrieved from server when needed\n      extractionModel: finalExtractionModel,\n      generationModel: finalGenerationModel,\n      model: finalGenerationModel, // Legacy field always matches generationModel\n    };\n  } catch (e) {\n    // console.error(\"Failed to parse AI provider settings, returning system defaults:\", e);\n    return systemDefaults;\n  }\n}\n\n/**\n * Check if AI provider settings are configured\n */\nexport function isAIProviderConfigured(): boolean {\n  const settings = getAIProviderSettings();\n  return Boolean(settings.apiKey.trim());\n}\n\n/**\n * Get list of available AI models by provider\n */\nexport function getAvailableModels(\n  provider: string\n): { id: string; name: string }[] {\n  switch (provider) {\n    case \"OpenRouter\":\n      return [\n        { id: \"google/gemini-2.5-flash-preview-05-20\", name: \"Google Gemini Flash 1.5\" },\n        {\n          id: \"google/gemini-2.5-pro-preview\",\n          name: \"Google Gemini Pro 1.5 (Recommended)\",\n        },\n      ];\n    default:\n      return [];\n  }\n}\n\n/**\n * Get available AI providers\n */\nexport function getAvailableProviders(): string[] {\n  return [\"OpenRouter\"];\n}\n\n/**\n * Get default base URL for a provider\n */\nexport function getDefaultBaseUrl(provider: string): string {\n  switch (provider) {\n    case \"OpenRouter\":\n      return \"https://openrouter.ai/api/v1\";\n    default:\n      return \"\";\n  }\n}\n\n/**\n * Get recommended model for specific tasks\n * This follows the PRD recommendations\n */\nexport function getRecommendedModelForTask(\n  task: \"extraction\" | \"generation\"\n): string {\n  const settings = getAIProviderSettings();\n  switch (task) {\n    case \"extraction\":\n      return settings.extractionModel || \"google/gemini-2.5-flash-preview-05-20\";\n    case \"generation\":\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n    default:\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n  }\n}\n", "path": "client/src/lib/ai-provider.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["5fd507b0-28a7-4f01-b6dd-c5b1ca166f87", {"value": {"selectedCode": "", "prefix": "import { AIProviderSettings } from \"@/types\";\n\n/**\n * Set AI provider settings (excluding API key which is stored securely on server)\n */\nexport function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {\n  // Store only non-sensitive configuration in localStorage\n  const safeSettings = {\n    provider: settings.provider,\n    baseUrl: settings.baseUrl,\n    extractionModel: settings.extractionModel,\n    generationModel: settings.generationModel,\n    model: settings.generationModel, // Keep model field aligned with generationModel\n    // Never store apiKey in localStorage\n  };\n  localStorage.setItem(\"aiProviderSettings\", JSON.stringify(safeSettings));\n}\n\n/**\n * Get AI provider settings from localStorage\n */\nexport function getAIProviderSettings(): AIProviderSettings {\n  const systemDefaultProvider = \"OpenRouter\";\n  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);\n  const systemDefaultModels = getAvailableModels(systemDefaultProvider);\n\n  const defaultExtractionModelId = \"google/gemini-2.5-flash-preview-05-20\";\n  const defaultGenerationModelId = \"google/gemini-2.5-pro-preview\";\n\n", "suffix": "  const systemDefaultExtractionModel =\n    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n  const systemDefaultGenerationModel =\n    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n\n  const systemDefaults: AIProviderSettings = {\n    provider: systemDefaultProvider,\n    baseUrl: systemDefaultBaseUrl,\n    apiKey: \"\", // Never use environment variables for API keys on client-side\n    extractionModel: systemDefaultExtractionModel,\n    generationModel: systemDefaultGenerationModel,\n    model: systemDefaultGenerationModel, // Legacy field, matches generationModel\n  };\n\n  const storedSettings = localStorage.getItem(\"aiProviderSettings\");\n  if (!storedSettings) {\n    return systemDefaults;\n  }\n\n  try {\n    const parsedSettings = JSON.parse(\n      storedSettings\n    ) as Partial<AIProviderSettings>;\n\n    const currentProvider = parsedSettings.provider || systemDefaults.provider;\n    const modelsForCurrentProvider = getAvailableModels(currentProvider);\n\n    let finalExtractionModel = parsedSettings.extractionModel;\n    if (\n      !finalExtractionModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)\n    ) {\n      finalExtractionModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.extractionModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    let finalGenerationModel = parsedSettings.generationModel;\n    if (\n      !finalGenerationModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)\n    ) {\n      finalGenerationModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.generationModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    return {\n      provider: currentProvider,\n      baseUrl:\n        parsedSettings.baseUrl ||\n        getDefaultBaseUrl(currentProvider) ||\n        systemDefaults.baseUrl,\n      apiKey: \"\", // API keys are never stored in localStorage - retrieved from server when needed\n      extractionModel: finalExtractionModel,\n      generationModel: finalGenerationModel,\n      model: finalGenerationModel, // Legacy field always matches generationModel\n    };\n  } catch (e) {\n    // console.error(\"Failed to parse AI provider settings, returning system defaults:\", e);\n    return systemDefaults;\n  }\n}\n\n/**\n * Check if AI provider settings are configured\n */\nexport function isAIProviderConfigured(): boolean {\n  const settings = getAIProviderSettings();\n  return Boolean(settings.apiKey.trim());\n}\n\n/**\n * Get list of available AI models by provider\n */\nexport function getAvailableModels(\n  provider: string\n): { id: string; name: string }[] {\n  switch (provider) {\n    case \"OpenRouter\":\n      return [\n        { id: \"google/gemini-2.5-flash-preview-05-20\", name: \"Google Gemini Flash 1.5\" },\n        {\n          id: \"google/gemini-2.5-pro-preview\",\n          name: \"Google Gemini Pro 1.5 (Recommended)\",\n        },\n      ];\n    default:\n      return [];\n  }\n}\n\n/**\n * Get available AI providers\n */\nexport function getAvailableProviders(): string[] {\n  return [\"OpenRouter\"];\n}\n\n/**\n * Get default base URL for a provider\n */\nexport function getDefaultBaseUrl(provider: string): string {\n  switch (provider) {\n    case \"OpenRouter\":\n      return \"https://openrouter.ai/api/v1\";\n    default:\n      return \"\";\n  }\n}\n\n/**\n * Get recommended model for specific tasks\n * This follows the PRD recommendations\n */\nexport function getRecommendedModelForTask(\n  task: \"extraction\" | \"generation\"\n): string {\n  const settings = getAIProviderSettings();\n  switch (task) {\n    case \"extraction\":\n      return settings.extractionModel || \"google/gemini-2.5-flash-preview-05-20\";\n    case \"generation\":\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n    default:\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n  }\n}\n", "path": "client/src/lib/ai-provider.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["38f38882-7f47-4256-addc-058eddfddf77", {"value": {"selectedCode": "", "prefix": "import { AIProviderSettings } from \"@/types\";\n\n/**\n * Set AI provider settings (excluding API key which is stored securely on server)\n */\nexport function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {\n  // Store only non-sensitive configuration in localStorage\n  const safeSettings = {\n    provider: settings.provider,\n    baseUrl: settings.baseUrl,\n    extractionModel: settings.extractionModel,\n    generationModel: settings.generationModel,\n    model: settings.generationModel, // Keep model field aligned with generationModel\n    // Never store apiKey in localStorage\n  };\n  localStorage.setItem(\"aiProviderSettings\", JSON.stringify(safeSettings));\n}\n\n/**\n * Get AI provider settings from localStorage\n */\nexport function getAIProviderSettings(): AIProviderSettings {\n  const systemDefaultProvider = \"OpenRouter\";\n  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);\n  const systemDefaultModels = getAvailableModels(systemDefaultProvider);\n", "suffix": "\n  const defaultExtractionModelId = \"google/gemini-2.5-flash-preview-05-20\";\n  const defaultGenerationModelId = \"google/gemini-2.5-pro-preview\";\n\n  const systemDefaultExtractionModel =\n    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n  const systemDefaultGenerationModel =\n    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n\n  const systemDefaults: AIProviderSettings = {\n    provider: systemDefaultProvider,\n    baseUrl: systemDefaultBaseUrl,\n    apiKey: \"\", // Never use environment variables for API keys on client-side\n    extractionModel: systemDefaultExtractionModel,\n    generationModel: systemDefaultGenerationModel,\n    model: systemDefaultGenerationModel, // Legacy field, matches generationModel\n  };\n\n  const storedSettings = localStorage.getItem(\"aiProviderSettings\");\n  if (!storedSettings) {\n    return systemDefaults;\n  }\n\n  try {\n    const parsedSettings = JSON.parse(\n      storedSettings\n    ) as Partial<AIProviderSettings>;\n\n    const currentProvider = parsedSettings.provider || systemDefaults.provider;\n    const modelsForCurrentProvider = getAvailableModels(currentProvider);\n\n    let finalExtractionModel = parsedSettings.extractionModel;\n    if (\n      !finalExtractionModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)\n    ) {\n      finalExtractionModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.extractionModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    let finalGenerationModel = parsedSettings.generationModel;\n    if (\n      !finalGenerationModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)\n    ) {\n      finalGenerationModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.generationModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    return {\n      provider: currentProvider,\n      baseUrl:\n        parsedSettings.baseUrl ||\n        getDefaultBaseUrl(currentProvider) ||\n        systemDefaults.baseUrl,\n      apiKey: \"\", // API keys are never stored in localStorage - retrieved from server when needed\n      extractionModel: finalExtractionModel,\n      generationModel: finalGenerationModel,\n      model: finalGenerationModel, // Legacy field always matches generationModel\n    };\n  } catch (e) {\n    // console.error(\"Failed to parse AI provider settings, returning system defaults:\", e);\n    return systemDefaults;\n  }\n}\n\n/**\n * Check if AI provider settings are configured\n */\nexport function isAIProviderConfigured(): boolean {\n  const settings = getAIProviderSettings();\n  return Boolean(settings.apiKey.trim());\n}\n\n/**\n * Get list of available AI models by provider\n */\nexport function getAvailableModels(\n  provider: string\n): { id: string; name: string }[] {\n  switch (provider) {\n    case \"OpenRouter\":\n      return [\n        { id: \"google/gemini-2.5-flash-preview-05-20\", name: \"Google Gemini Flash 1.5\" },\n        {\n          id: \"google/gemini-2.5-pro-preview\",\n          name: \"Google Gemini Pro 1.5 (Recommended)\",\n        },\n      ];\n    default:\n      return [];\n  }\n}\n\n/**\n * Get available AI providers\n */\nexport function getAvailableProviders(): string[] {\n  return [\"OpenRouter\"];\n}\n\n/**\n * Get default base URL for a provider\n */\nexport function getDefaultBaseUrl(provider: string): string {\n  switch (provider) {\n    case \"OpenRouter\":\n      return \"https://openrouter.ai/api/v1\";\n    default:\n      return \"\";\n  }\n}\n\n/**\n * Get recommended model for specific tasks\n * This follows the PRD recommendations\n */\nexport function getRecommendedModelForTask(\n  task: \"extraction\" | \"generation\"\n): string {\n  const settings = getAIProviderSettings();\n  switch (task) {\n    case \"extraction\":\n      return settings.extractionModel || \"google/gemini-2.5-flash-preview-05-20\";\n    case \"generation\":\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n    default:\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n  }\n}\n", "path": "client/src/lib/ai-provider.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["7e173940-a03d-460c-8e7b-d6c62b60e429", {"value": {"selectedCode": "", "prefix": "import { AIProviderSettings } from \"@/types\";\n\n/**\n * Set AI provider settings (excluding API key which is stored securely on server)\n */\nexport function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {\n  // Store only non-sensitive configuration in localStorage\n  const safeSettings = {\n    provider: settings.provider,\n    baseUrl: settings.baseUrl,\n    extractionModel: settings.extractionModel,\n    generationModel: settings.generationModel,\n    model: settings.generationModel, // Keep model field aligned with generationModel\n    // Never store apiKey in localStorage\n  };\n  localStorage.setItem(\"aiProviderSettings\", JSON.stringify(safeSettings));\n}\n\n/**\n * Get AI provider settings from localStorage\n */\nexport function getAIProviderSettings(): AIProviderSettings {\n  const systemDefaultProvider = \"OpenRouter\";\n  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);\n  const systemDefaultModels = getAvailableModels(systemDefaultProvider);\n\n  const defaultExtractionModelId = \"google/gemini-2.5-flash-preview-05-20\";\n  const defaultGenerationModelId = \"google/gemini-2.5-pro-preview\";\n\n  const systemDefaultExtractionModel =\n    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n  const systemDefaultGenerationModel =\n    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n\n  const systemDefaults: AIProviderSettings = {\n    provider: systemDefaultProvider,\n    baseUrl: systemDefaultBaseUrl,\n    apiKey: \"\", // Never use environment variables for API keys on client-side\n    extractionModel: systemDefaultExtractionModel,\n    generationModel: systemDefaultGenerationModel,\n    model: systemDefaultGenerationModel, // Legacy field, matches generationModel\n  };\n\n  const storedSettings = localStorage.getItem(\"aiProviderSettings\");\n  if (!storedSettings) {\n    return systemDefaults;\n  }\n\n  try {\n    const parsedSettings = JSON.parse(\n      storedSettings\n    ) as Partial<AIProviderSettings>;\n\n    const currentProvider = parsedSettings.provider || systemDefaults.provider;\n    const modelsForCurrentProvider = getAvailableModels(currentProvider);\n\n    let finalExtractionModel = parsedSettings.extractionModel;\n    if (\n      !finalExtractionModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)\n    ) {\n      finalExtractionModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.extractionModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    let finalGenerationModel = parsedSettings.generationModel;\n    if (\n      !finalGenerationModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)\n    ) {\n      finalGenerationModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.generationModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    return {\n      provider: currentProvider,\n      baseUrl:\n        parsedSettings.baseUrl ||\n        getDefaultBaseUrl(currentProvider) ||\n        systemDefaults.baseUrl,\n      apiKey: \"\", // API keys are never stored in localStorage - retrieved from server when needed\n      extractionModel: finalExtractionModel,\n      generationModel: finalGenerationModel,\n      model: finalGenerationModel, // Legacy field always matches generationModel\n    };\n  } catch (e) {\n    // console.error(\"Failed to parse AI provider settings, returning system defaults:\", e);\n    return systemDefaults;\n  }\n}\n\n/**\n * Check if AI provider settings are configured\n */\nexport function isAIProviderConfigured(): boolean {\n  const settings = getAIProviderSettings();\n  return Boolean(settings.apiKey.trim());\n}\n\n/**\n * Get list of available AI models by provider\n */\nexport function getAvailableModels(\n  provider: string\n): { id: string; name: string }[] {\n  switch (provider) {\n    case \"OpenRouter\":\n      return [\n        { id: \"google/gemini-2.5-flash-preview-05-20\", name: \"Google Gemini Flash 1.5\" },\n        {\n          id: \"google/gemini-2.5-pro-preview\",\n          name: \"Google Gemini Pro 1.5 (Recommended)\",\n", "suffix": "        },\n      ];\n    default:\n      return [];\n  }\n}\n\n/**\n * Get available AI providers\n */\nexport function getAvailableProviders(): string[] {\n  return [\"OpenRouter\"];\n}\n\n/**\n * Get default base URL for a provider\n */\nexport function getDefaultBaseUrl(provider: string): string {\n  switch (provider) {\n    case \"OpenRouter\":\n      return \"https://openrouter.ai/api/v1\";\n    default:\n      return \"\";\n  }\n}\n\n/**\n * Get recommended model for specific tasks\n * This follows the PRD recommendations\n */\nexport function getRecommendedModelForTask(\n  task: \"extraction\" | \"generation\"\n): string {\n  const settings = getAIProviderSettings();\n  switch (task) {\n    case \"extraction\":\n      return settings.extractionModel || \"google/gemini-2.5-flash-preview-05-20\";\n    case \"generation\":\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n    default:\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n  }\n}\n", "path": "client/src/lib/ai-provider.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["f7ab21c5-53b4-4d1a-8aa9-c0395b8ddc06", {"value": {"selectedCode": "", "prefix": "import { AIProviderSettings } from \"@/types\";\n\n/**\n * Set AI provider settings (excluding API key which is stored securely on server)\n */\nexport function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {\n  // Store only non-sensitive configuration in localStorage\n  const safeSettings = {\n    provider: settings.provider,\n    baseUrl: settings.baseUrl,\n    extractionModel: settings.extractionModel,\n    generationModel: settings.generationModel,\n    model: settings.generationModel, // Keep model field aligned with generationModel\n    // Never store apiKey in localStorage\n  };\n  localStorage.setItem(\"aiProviderSettings\", JSON.stringify(safeSettings));\n}\n\n/**\n * Get AI provider settings from localStorage\n */\nexport function getAIProviderSettings(): AIProviderSettings {\n  const systemDefaultProvider = \"OpenRouter\";\n  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);\n  const systemDefaultModels = getAvailableModels(systemDefaultProvider);\n\n  const defaultExtractionModelId = \"google/gemini-2.5-flash-preview-05-20\";\n  const defaultGenerationModelId = \"google/gemini-2.5-pro-preview\";\n\n  const systemDefaultExtractionModel =\n    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n  const systemDefaultGenerationModel =\n    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n\n  const systemDefaults: AIProviderSettings = {\n    provider: systemDefaultProvider,\n    baseUrl: systemDefaultBaseUrl,\n    apiKey: \"\", // Never use environment variables for API keys on client-side\n    extractionModel: systemDefaultExtractionModel,\n    generationModel: systemDefaultGenerationModel,\n    model: systemDefaultGenerationModel, // Legacy field, matches generationModel\n  };\n\n  const storedSettings = localStorage.getItem(\"aiProviderSettings\");\n  if (!storedSettings) {\n    return systemDefaults;\n  }\n\n  try {\n    const parsedSettings = JSON.parse(\n      storedSettings\n    ) as Partial<AIProviderSettings>;\n\n    const currentProvider = parsedSettings.provider || systemDefaults.provider;\n    const modelsForCurrentProvider = getAvailableModels(currentProvider);\n\n    let finalExtractionModel = parsedSettings.extractionModel;\n    if (\n      !finalExtractionModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)\n    ) {\n      finalExtractionModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.extractionModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    let finalGenerationModel = parsedSettings.generationModel;\n    if (\n      !finalGenerationModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)\n    ) {\n      finalGenerationModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.generationModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    return {\n      provider: currentProvider,\n      baseUrl:\n        parsedSettings.baseUrl ||\n        getDefaultBaseUrl(currentProvider) ||\n        systemDefaults.baseUrl,\n      apiKey: \"\", // API keys are never stored in localStorage - retrieved from server when needed\n      extractionModel: finalExtractionModel,\n      generationModel: finalGenerationModel,\n      model: finalGenerationModel, // Legacy field always matches generationModel\n    };\n  } catch (e) {\n    // console.error(\"Failed to parse AI provider settings, returning system defaults:\", e);\n    return systemDefaults;\n  }\n}\n\n/**\n * Check if AI provider settings are configured\n */\nexport function isAIProviderConfigured(): boolean {\n  const settings = getAIProviderSettings();\n  return Boolean(settings.apiKey.trim());\n}\n\n/**\n * Get list of available AI models by provider\n */\nexport function getAvailableModels(\n  provider: string\n): { id: string; name: string }[] {\n  switch (provider) {\n    case \"OpenRouter\":\n      return [\n        { id: \"google/gemini-2.5-flash-preview-05-20\", name: \"Google Gemini Flash 2.5\" },\n        {\n          id: \"google/gemini-2.5-pro-preview\",\n          name: \"Google Gemini Pro 2.5 (Recommended)\",\n        },\n      ];\n    default:\n      return [];\n  }\n}\n\n/**\n * Get available AI providers\n */\nexport function getAvailableProviders(): string[] {\n  return [\"OpenRouter\"];\n}\n\n/**\n * Get default base URL for a provider\n */\nexport function getDefaultBaseUrl(provider: string): string {\n  switch (provider) {\n    case \"OpenRouter\":\n      return \"https://openrouter.ai/api/v1\";\n    default:\n      return \"\";\n  }\n}\n\n/**\n * Get recommended model for specific tasks\n * This follows the PRD recommendations\n */\nexport function getRecommendedModelForTask(\n  task: \"extraction\" | \"generation\"\n): string {\n  const settings = getAIProviderSettings();\n  switch (task) {\n    case \"extraction\":\n      return settings.extractionModel || \"google/gemini-2.5-flash-preview-05-20\";\n    case \"generation\":\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n    default:\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n  }\n}\n", "suffix": "", "path": "client/src/lib/ai-provider.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["b91ead33-e6d7-4389-b6b7-12fca00196e5", {"value": {"selectedCode": "", "prefix": "import { AIProviderSettings } from \"@/types\";\n\n/**\n * Set AI provider settings (excluding API key which is stored securely on server)\n */\nexport function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {\n  // Store only non-sensitive configuration in localStorage\n  const safeSettings = {\n    provider: settings.provider,\n    baseUrl: settings.baseUrl,\n    extractionModel: settings.extractionModel,\n    generationModel: settings.generationModel,\n    model: settings.generationModel, // Keep model field aligned with generationModel\n    // Never store apiKey in localStorage\n  };\n  localStorage.setItem(\"aiProviderSettings\", JSON.stringify(safeSettings));\n}\n\n/**\n * Get AI provider settings from localStorage\n */\nexport function getAIProviderSettings(): AIProviderSettings {\n  const systemDefaultProvider = \"OpenRouter\";\n  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);\n  const systemDefaultModels = getAvailableModels(systemDefaultProvider);\n\n  const defaultExtractionModelId = \"google/gemini-2.5-flash-preview-05-20\";\n  const defaultGenerationModelId = \"google/gemini-2.5-pro-preview\";\n\n  const systemDefaultExtractionModel =\n    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n  const systemDefaultGenerationModel =\n    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n\n  const systemDefaults: AIProviderSettings = {\n    provider: systemDefaultProvider,\n    baseUrl: systemDefaultBaseUrl,\n    apiKey: \"\", // Never use environment variables for API keys on client-side\n    extractionModel: systemDefaultExtractionModel,\n    generationModel: systemDefaultGenerationModel,\n    model: systemDefaultGenerationModel, // Legacy field, matches generationModel\n  };\n\n  const storedSettings = localStorage.getItem(\"aiProviderSettings\");\n  if (!storedSettings) {\n    return systemDefaults;\n  }\n\n  try {\n    const parsedSettings = JSON.parse(\n      storedSettings\n    ) as Partial<AIProviderSettings>;\n\n    const currentProvider = parsedSettings.provider || systemDefaults.provider;\n    const modelsForCurrentProvider = getAvailableModels(currentProvider);\n\n    let finalExtractionModel = parsedSettings.extractionModel;\n    if (\n      !finalExtractionModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)\n    ) {\n      finalExtractionModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.extractionModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    let finalGenerationModel = parsedSettings.generationModel;\n    if (\n      !finalGenerationModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)\n    ) {\n      finalGenerationModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.generationModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    return {\n      provider: currentProvider,\n      baseUrl:\n        parsedSettings.baseUrl ||\n        getDefaultBaseUrl(currentProvider) ||\n        systemDefaults.baseUrl,\n      apiKey: \"\", // API keys are never stored in localStorage - retrieved from server when needed\n      extractionModel: finalExtractionModel,\n      generationModel: finalGenerationModel,\n      model: finalGenerationModel, // Legacy field always matches generationModel\n    };\n  } catch (e) {\n    // console.error(\"Failed to parse AI provider settings, returning system defaults:\", e);\n    return systemDefaults;\n  }\n}\n\n/**\n * Check if AI provider settings are configured\n */\nexport function isAIProviderConfigured(): boolean {\n  const settings = getAIProviderSettings();\n  return Boolean(settings.apiKey.trim());\n}\n\n/**\n * Get list of available AI models by provider\n */\nexport function getAvailableModels(\n  provider: string\n): { id: string; name: string }[] {\n  switch (provider) {\n    case \"OpenRouter\":\n      return [\n        { id: \"google/gemini-2.5-flash-preview-05-20\", name: \"Google Gemini Flash 2.5\" },\n        {\n          id: \"google/gemini-2.5-pro-preview\",\n          name: \"Google Gemini Pro 2.5 (Recommended)\",\n        },\n      ];\n    default:\n      return [];\n  }\n}\n\n/**\n * Get available AI providers\n */\nexport function getAvailableProviders(): string[] {\n  return [\"OpenRouter\"];\n}\n\n/**\n * Get default base URL for a provider\n */\nexport function getDefaultBaseUrl(provider: string): string {\n  switch (provider) {\n    case \"OpenRouter\":\n      return \"https://openrouter.ai/api/v1\";\n    default:\n      return \"\";\n  }\n}\n\n/**\n * Get recommended model for specific tasks\n * This follows the PRD recommendations\n */\nexport function getRecommendedModelForTask(\n  task: \"extraction\" | \"generation\"\n): string {\n  const settings = getAIProviderSettings();\n  switch (task) {\n    case \"extraction\":\n      return settings.extractionModel || \"google/gemini-2.5-flash-preview-05-20\";\n    case \"generation\":\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n    default:\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n  }\n}\n", "suffix": "", "path": "client/src/lib/ai-provider.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["0cec4bae-ca48-4111-bf10-2dbfdc15a98a", {"value": {"selectedCode": "", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash and generation model to Pro Preview\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = (values: AIConfigFormValues) => {\n    // Set the legacy 'model' field to match generationModel for backward compatibility\n    const settingsToSave = {\n      ...values,\n      model: values.generationModel,\n    };\n\n    setAIProviderSettings(settingsToSave);\n    toast({\n      title: \"AI Provider Configured\",\n      description: \"Your AI settings have been saved successfully.\",\n    });\n    navigate(\"/\");\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash for extraction and Pro Preview for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n", "suffix": "          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is used securely and never stored\n                      permanently.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is sent securely to our backend and used only\n                    for your current session. We don't store your credentials\n                    permanently. All AI requests are processed through your\n                    configured provider using your own API key, so you maintain\n                    control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                Save Configuration\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["d10ea276-6aae-49e1-9a10-5a0832b87039", {"value": {"selectedCode": "", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash-preview-05-20\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = (values: AIConfigFormValues) => {\n    // Set the legacy 'model' field to match generationModel for backward compatibility\n    const settingsToSave = {\n      ...values,\n      model: values.generationModel,\n    };\n\n    setAIProviderSettings(settingsToSave);\n    toast({\n      title: \"AI Provider Configured\",\n      description: \"Your AI settings have been saved successfully.\",\n    });\n    navigate(\"/\");\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n", "suffix": "              models - Flash 2.5 for extraction and Pro 2.5 for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is used securely and never stored\n                      permanently.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is sent securely to our backend and used only\n                    for your current session. We don't store your credentials\n                    permanently. All AI requests are processed through your\n                    configured provider using your own API key, so you maintain\n                    control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                Save Configuration\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["688aa785-cee5-4752-88ac-80b7fe3df5e1", {"value": {"selectedCode": "", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash-preview-05-20\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = async (values: AIConfigFormValues) => {\n    setIsSubmitting(true);\n\n    try {\n      // First, validate the API key is provided\n      if (!values.apiKey.trim()) {\n        toast({\n          title: \"API Key Required\",\n          description: \"Please enter your API key to save the configuration.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Store credentials securely on the backend\n      await storeCredentialsAPI({\n        provider: values.provider,\n        apiKey: values.apiKey,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n      });\n\n      // Store non-sensitive settings in localStorage for quick access\n      const settingsToSave = {\n        provider: values.provider,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n        model: values.generationModel, // Legacy field for backward compatibility\n      };\n      setAIProviderSettings(settingsToSave);\n\n      toast({\n        title: \"AI Provider Configured\",\n        description: \"Your API credentials have been saved securely and your settings are now active.\",\n      });\n\n      navigate(\"/\");\n    } catch (error) {\n      console.error(\"Failed to save AI configuration:\", error);\n      toast({\n        title: \"Configuration Save Failed\",\n        description: error instanceof Error\n          ? error.message\n          : \"Failed to save your AI configuration. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n", "suffix": "              models - Flash 2.5 for extraction and Pro 2.5 for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is used securely and never stored\n                      permanently.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is sent securely to our backend and used only\n                    for your current session. We don't store your credentials\n                    permanently. All AI requests are processed through your\n                    configured provider using your own API key, so you maintain\n                    control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                Save Configuration\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["236f9d61-e904-4d2d-94d7-ebb11c49c0f3", {"value": {"selectedCode": "", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash-preview-05-20\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = async (values: AIConfigFormValues) => {\n    setIsSubmitting(true);\n\n    try {\n      // First, validate the API key is provided\n      if (!values.apiKey.trim()) {\n        toast({\n          title: \"API Key Required\",\n          description: \"Please enter your API key to save the configuration.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Store credentials securely on the backend\n      await storeCredentialsAPI({\n        provider: values.provider,\n        apiKey: values.apiKey,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n      });\n\n      // Store non-sensitive settings in localStorage for quick access\n      const settingsToSave = {\n        provider: values.provider,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n        model: values.generationModel, // Legacy field for backward compatibility\n      };\n      setAIProviderSettings(settingsToSave);\n\n      toast({\n        title: \"AI Provider Configured\",\n        description: \"Your API credentials have been saved securely and your settings are now active.\",\n      });\n\n      navigate(\"/\");\n    } catch (error) {\n      console.error(\"Failed to save AI configuration:\", error);\n      toast({\n        title: \"Configuration Save Failed\",\n        description: error instanceof Error\n          ? error.message\n          : \"Failed to save your AI configuration. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash 2.5 for extraction and Pro 2.5 for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is used securely and never stored\n                      permanently.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is sent securely to our backend and used only\n                    for your current session. We don't store your credentials\n                    permanently. All AI requests are processed through your\n                    configured provider using your own API key, so you maintain\n                    control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n", "suffix": "                disabled={isSubmitting}\n              >\n                Save Configuration\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["b42f7949-7d6f-4c4e-b855-78b772f6a449", {"value": {"selectedCode": "", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash-preview-05-20\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = async (values: AIConfigFormValues) => {\n    setIsSubmitting(true);\n\n    try {\n      // First, validate the API key is provided\n      if (!values.apiKey.trim()) {\n        toast({\n          title: \"API Key Required\",\n          description: \"Please enter your API key to save the configuration.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Store credentials securely on the backend\n      await storeCredentialsAPI({\n        provider: values.provider,\n        apiKey: values.apiKey,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n      });\n\n      // Store non-sensitive settings in localStorage for quick access\n      const settingsToSave = {\n        provider: values.provider,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n        model: values.generationModel, // Legacy field for backward compatibility\n      };\n      setAIProviderSettings(settingsToSave);\n\n      toast({\n        title: \"AI Provider Configured\",\n        description: \"Your API credentials have been saved securely and your settings are now active.\",\n      });\n\n      navigate(\"/\");\n    } catch (error) {\n      console.error(\"Failed to save AI configuration:\", error);\n      toast({\n        title: \"Configuration Save Failed\",\n        description: error instanceof Error\n          ? error.message\n          : \"Failed to save your AI configuration. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash 2.5 for extraction and Pro 2.5 for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is used securely and never stored\n                      permanently.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is sent securely to our backend and used only\n                    for your current session. We don't store your credentials\n                    permanently. All AI requests are processed through your\n                    configured provider using your own API key, so you maintain\n                    control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                disabled={isSubmitting}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300 disabled:opacity-50\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={isSubmitting}\n", "suffix": "                className=\"bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Saving...\n                  </>\n                ) : (\n                  \"Save Configuration\"\n                )}\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["7b6bc484-c736-4ce8-86cc-badf9c80ffd4", {"value": {"selectedCode": "", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash-preview-05-20\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = async (values: AIConfigFormValues) => {\n    setIsSubmitting(true);\n\n    try {\n      // First, validate the API key is provided\n      if (!values.apiKey.trim()) {\n        toast({\n          title: \"API Key Required\",\n          description: \"Please enter your API key to save the configuration.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Store credentials securely on the backend\n      await storeCredentialsAPI({\n        provider: values.provider,\n        apiKey: values.apiKey,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n      });\n\n      // Store non-sensitive settings in localStorage for quick access\n      const settingsToSave = {\n        provider: values.provider,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n        model: values.generationModel, // Legacy field for backward compatibility\n      };\n      setAIProviderSettings(settingsToSave);\n\n      toast({\n        title: \"AI Provider Configured\",\n        description: \"Your API credentials have been saved securely and your settings are now active.\",\n      });\n\n      navigate(\"/\");\n    } catch (error) {\n      console.error(\"Failed to save AI configuration:\", error);\n      toast({\n        title: \"Configuration Save Failed\",\n        description: error instanceof Error\n          ? error.message\n          : \"Failed to save your AI configuration. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash 2.5 for extraction and Pro 2.5 for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is used securely and never stored\n                      permanently.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is encrypted and stored securely in our database.\n                    It's only decrypted when making AI requests on your behalf.\n                    All AI requests are processed through your configured provider\n                    using your own API key, so you maintain control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                disabled={isSubmitting}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300 disabled:opacity-50\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={isSubmitting}\n", "suffix": "                className=\"bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Saving...\n                  </>\n                ) : (\n                  \"Save Configuration\"\n                )}\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["64941383-1993-4222-aabe-3aed8f52d898", {"value": {"selectedCode": "", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash-preview-05-20\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = async (values: AIConfigFormValues) => {\n    setIsSubmitting(true);\n\n    try {\n      // First, validate the API key is provided\n      if (!values.apiKey.trim()) {\n        toast({\n          title: \"API Key Required\",\n          description: \"Please enter your API key to save the configuration.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Store credentials securely on the backend\n      await storeCredentialsAPI({\n        provider: values.provider,\n        apiKey: values.apiKey,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n      });\n\n      // Store non-sensitive settings in localStorage for quick access\n      const settingsToSave = {\n        provider: values.provider,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n        model: values.generationModel, // Legacy field for backward compatibility\n      };\n      setAIProviderSettings(settingsToSave);\n\n      toast({\n        title: \"AI Provider Configured\",\n        description: \"Your API credentials have been saved securely and your settings are now active.\",\n      });\n\n      navigate(\"/\");\n    } catch (error) {\n      console.error(\"Failed to save AI configuration:\", error);\n      toast({\n        title: \"Configuration Save Failed\",\n        description: error instanceof Error\n          ? error.message\n          : \"Failed to save your AI configuration. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash 2.5 for extraction and Pro 2.5 for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is encrypted and stored securely for AI operations.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is encrypted and stored securely in our database.\n                    It's only decrypted when making AI requests on your behalf.\n                    All AI requests are processed through your configured provider\n                    using your own API key, so you maintain control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                disabled={isSubmitting}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300 disabled:opacity-50\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={isSubmitting}\n", "suffix": "                className=\"bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Saving...\n                  </>\n                ) : (\n                  \"Save Configuration\"\n                )}\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["52d931dc-7bbe-49d7-80f5-9f4c0c09c555", {"value": {"selectedCode": "", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash-preview-05-20\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = async (values: AIConfigFormValues) => {\n    setIsSubmitting(true);\n\n    try {\n      // First, validate the API key is provided\n      if (!values.apiKey.trim()) {\n        toast({\n          title: \"API Key Required\",\n          description: \"Please enter your API key to save the configuration.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Store credentials securely on the backend\n      await storeCredentialsAPI({\n        provider: values.provider,\n        apiKey: values.apiKey,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n      });\n\n      // Store non-sensitive settings in localStorage for quick access\n      const settingsToSave = {\n        provider: values.provider,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n        model: values.generationModel, // Legacy field for backward compatibility\n      };\n      setAIProviderSettings(settingsToSave);\n\n      toast({\n        title: \"AI Provider Configured\",\n        description: \"Your API credentials have been saved securely and your settings are now active.\",\n      });\n\n      navigate(\"/\");\n    } catch (error) {\n      console.error(\"Failed to save AI configuration:\", error);\n      toast({\n        title: \"Configuration Save Failed\",\n        description: error instanceof Error\n          ? error.message\n          : \"Failed to save your AI configuration. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash 2.5 for extraction and Pro 2.5 for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is encrypted and stored securely for AI operations.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is encrypted and stored securely in our database.\n                    It's only decrypted when making AI requests on your behalf.\n                    All AI requests are processed through your configured provider\n                    using your own API key, so you maintain control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                disabled={isSubmitting}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300 disabled:opacity-50\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Saving...\n                  </>\n                ) : (\n", "suffix": "                  \"Save Configuration\"\n                )}\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["8fd9d1df-b201-40a6-a122-8848b79f8a17", {"value": {"selectedCode": "", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash-preview-05-20\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = async (values: AIConfigFormValues) => {\n    setIsSubmitting(true);\n\n    try {\n      // First, validate the API key is provided\n      if (!values.apiKey.trim()) {\n        toast({\n          title: \"API Key Required\",\n          description: \"Please enter your API key to save the configuration.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Store credentials securely on the backend\n      await storeCredentialsAPI({\n        provider: values.provider,\n        apiKey: values.apiKey,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n      });\n\n      // Store non-sensitive settings in localStorage for quick access\n      const settingsToSave = {\n        provider: values.provider,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n        model: values.generationModel, // Legacy field for backward compatibility\n      };\n      setAIProviderSettings(settingsToSave);\n\n      toast({\n        title: \"AI Provider Configured\",\n        description: \"Your API credentials have been saved securely and your settings are now active.\",\n      });\n\n      navigate(\"/\");\n    } catch (error) {\n      console.error(\"Failed to save AI configuration:\", error);\n      toast({\n        title: \"Configuration Save Failed\",\n        description: error instanceof Error\n          ? error.message\n          : \"Failed to save your AI configuration. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash 2.5 for extraction and Pro 2.5 for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is encrypted and stored securely for AI operations.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is encrypted and stored securely in our database.\n                    It's only decrypted when making AI requests on your behalf.\n                    All AI requests are processed through your configured provider\n                    using your own API key, so you maintain control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                disabled={isSubmitting}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300 disabled:opacity-50\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Saving...\n                  </>\n                ) : (\n                  \"Save Configuration\"\n                )}\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\n", "suffix": "export default AIConfigurationSection;\n", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["bc9a9274-7969-40b2-9ef4-44fca40a55b4", {"value": {"selectedCode": "", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash-preview-05-20\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = async (values: AIConfigFormValues) => {\n    setIsSubmitting(true);\n\n    try {\n      // First, validate the API key is provided\n      if (!values.apiKey.trim()) {\n        toast({\n          title: \"API Key Required\",\n          description: \"Please enter your API key to save the configuration.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Store credentials securely on the backend\n      await storeCredentialsAPI({\n        provider: values.provider,\n        apiKey: values.apiKey,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n      });\n\n      // Store non-sensitive settings in localStorage for quick access\n      const settingsToSave = {\n        provider: values.provider,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n        model: values.generationModel, // Legacy field for backward compatibility\n      };\n      setAIProviderSettings(settingsToSave);\n\n      toast({\n        title: \"AI Provider Configured\",\n        description: \"Your API credentials have been saved securely and your settings are now active.\",\n      });\n\n      navigate(\"/\");\n    } catch (error) {\n      console.error(\"Failed to save AI configuration:\", error);\n      toast({\n        title: \"Configuration Save Failed\",\n        description: error instanceof Error\n          ? error.message\n          : \"Failed to save your AI configuration. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash 2.5 for extraction and Pro 2.5 for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is encrypted and stored securely for AI operations.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is encrypted and stored securely in our database.\n                    It's only decrypted when making AI requests on your behalf.\n                    All AI requests are processed through your configured provider\n                    using your own API key, so you maintain control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                disabled={isSubmitting}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300 disabled:opacity-50\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Saving...\n                  </>\n                ) : (\n                  \"Save Configuration\"\n                )}\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n", "suffix": "    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["cedc5a5c-927f-4bda-bf93-36aadbea3ed7", {"value": {"selectedCode": "", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash-preview-05-20\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = async (values: AIConfigFormValues) => {\n    setIsSubmitting(true);\n\n    try {\n      // First, validate the API key is provided\n      if (!values.apiKey.trim()) {\n        toast({\n          title: \"API Key Required\",\n          description: \"Please enter your API key to save the configuration.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Store credentials securely on the backend\n      await storeCredentialsAPI({\n        provider: values.provider,\n        apiKey: values.apiKey,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n      });\n\n      // Store non-sensitive settings in localStorage for quick access\n      const settingsToSave = {\n        provider: values.provider,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n        model: values.generationModel, // Legacy field for backward compatibility\n      };\n      setAIProviderSettings(settingsToSave);\n\n      toast({\n        title: \"AI Provider Configured\",\n        description: \"Your API credentials have been saved securely and your settings are now active.\",\n      });\n\n      navigate(\"/\");\n    } catch (error) {\n      console.error(\"Failed to save AI configuration:\", error);\n      toast({\n        title: \"Configuration Save Failed\",\n        description: error instanceof Error\n          ? error.message\n          : \"Failed to save your AI configuration. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash 2.5 for extraction and Pro 2.5 for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is encrypted and stored securely for AI operations.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is encrypted and stored securely in our database.\n                    It's only decrypted when making AI requests on your behalf.\n                    All AI requests are processed through your configured provider\n                    using your own API key, so you maintain control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                disabled={isSubmitting}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300 disabled:opacity-50\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Saving...\n                  </>\n                ) : (\n                  \"Save Configuration\"\n                )}\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\n", "suffix": "export default AIConfigurationSection;\n", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["c4ba3056-4302-4776-a807-83d847fcabe1", {"value": {"selectedCode": "", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash-preview-05-20\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = async (values: AIConfigFormValues) => {\n    setIsSubmitting(true);\n\n    try {\n      // First, validate the API key is provided\n      if (!values.apiKey.trim()) {\n        toast({\n          title: \"API Key Required\",\n          description: \"Please enter your API key to save the configuration.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Store credentials securely on the backend\n      await storeCredentialsAPI({\n        provider: values.provider,\n        apiKey: values.apiKey,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n      });\n\n      // Store non-sensitive settings in localStorage for quick access\n      const settingsToSave = {\n        provider: values.provider,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n        model: values.generationModel, // Legacy field for backward compatibility\n      };\n      setAIProviderSettings(settingsToSave);\n\n      toast({\n        title: \"AI Provider Configured\",\n        description: \"Your API credentials have been saved securely and your settings are now active.\",\n      });\n\n      navigate(\"/\");\n    } catch (error) {\n      console.error(\"Failed to save AI configuration:\", error);\n      toast({\n        title: \"Configuration Save Failed\",\n        description: error instanceof Error\n          ? error.message\n          : \"Failed to save your AI configuration. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash 2.5 for extraction and Pro 2.5 for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is encrypted and stored securely for AI operations.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is encrypted and stored securely in our database.\n                    It's only decrypted when making AI requests on your behalf.\n                    All AI requests are processed through your configured provider\n                    using your own API key, so you maintain control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                disabled={isSubmitting}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300 disabled:opacity-50\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Saving...\n                  </>\n                ) : (\n                  \"Save Configuration\"\n                )}\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "suffix": "", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["80759362-c782-4e4f-86cb-07fa027a1cca", {"value": {"selectedCode": "", "prefix": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash-preview-05-20\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = async (values: AIConfigFormValues) => {\n    setIsSubmitting(true);\n\n    try {\n      // First, validate the API key is provided\n      if (!values.apiKey.trim()) {\n        toast({\n          title: \"API Key Required\",\n          description: \"Please enter your API key to save the configuration.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Store credentials securely on the backend\n      await storeCredentialsAPI({\n        provider: values.provider,\n        apiKey: values.apiKey,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n      });\n\n      // Store non-sensitive settings in localStorage for quick access\n      const settingsToSave = {\n        provider: values.provider,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n        model: values.generationModel, // Legacy field for backward compatibility\n      };\n      setAIProviderSettings(settingsToSave);\n\n      toast({\n        title: \"AI Provider Configured\",\n        description: \"Your API credentials have been saved securely and your settings are now active.\",\n      });\n\n      navigate(\"/\");\n    } catch (error) {\n      console.error(\"Failed to save AI configuration:\", error);\n      toast({\n        title: \"Configuration Save Failed\",\n        description: error instanceof Error\n          ? error.message\n          : \"Failed to save your AI configuration. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash 2.5 for extraction and Pro 2.5 for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is encrypted and stored securely for AI operations.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is encrypted and stored securely in our database.\n                    It's only decrypted when making AI requests on your behalf.\n                    All AI requests are processed through your configured provider\n                    using your own API key, so you maintain control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                disabled={isSubmitting}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300 disabled:opacity-50\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Saving...\n                  </>\n                ) : (\n                  \"Save Configuration\"\n                )}\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "suffix": "", "path": "client/src/components/ai/AIConfigurationSection.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}]]