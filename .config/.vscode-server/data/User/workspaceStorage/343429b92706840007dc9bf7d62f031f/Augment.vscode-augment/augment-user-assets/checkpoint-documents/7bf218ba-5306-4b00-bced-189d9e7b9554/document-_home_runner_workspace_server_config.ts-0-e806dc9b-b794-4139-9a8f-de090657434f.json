{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/config.ts"}, "originalCode": "// Environment variables are set directly in deployment\n// No need for dotenv in production builds\n\n// Direct configuration with updated values\nexport const supabaseConfig = {\n  url: \"https://hrdjfukhzbzksqaupqie.supabase.co\",\n  serviceRoleKey:\n    \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0\",\n  dbPassword: process.env.VITE_DATABASE_PASSWORD || \"\",\n};\n\n// Validation - only check essential config\nif (!supabaseConfig.url || !supabaseConfig.serviceRoleKey) {\n  console.error(\n    \"❌ Error: Supabase URL and Service Role Key must be configured.\"\n  );\n  console.error(\"Current config:\", {\n    url: supabaseConfig.url ? \"✓ Set\" : \"✗ Missing\",\n    serviceRoleKey: supabaseConfig.serviceRoleKey ? \"✓ Set\" : \"✗ Missing\",\n  });\n  process.exit(1);\n}\n\n// Log configuration status\nconsole.log(\"✓ Supabase configuration loaded successfully\");\nconsole.log(\"✓ URL:\", supabaseConfig.url ? \"Configured\" : \"Missing\");\nconsole.log(\n  \"✓ Service Role Key:\",\n  supabaseConfig.serviceRoleKey ? \"Configured\" : \"Missing\"\n);\nconsole.log(\n  \"✓ Database Password:\",\n  supabaseConfig.dbPassword\n    ? \"Configured\"\n    : \"Optional - using service role key authentication\"\n);\n", "modifiedCode": "// Environment variables are set directly in deployment\n// No need for dotenv in production builds\n\n// Direct configuration with updated values\nexport const supabaseConfig = {\n  url: \"https://hrdjfukhzbzksqaupqie.supabase.co\",\n  serviceRoleKey:\n    \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0\",\n  dbPassword: process.env.VITE_DATABASE_PASSWORD || \"\",\n};\n\n// Validation - only check essential config\nif (!supabaseConfig.url || !supabaseConfig.serviceRoleKey) {\n  console.error(\n    \"❌ Error: Supabase URL and Service Role Key must be configured.\"\n  );\n  console.error(\"Current config:\", {\n    url: supabaseConfig.url ? \"✓ Set\" : \"✗ Missing\",\n    serviceRoleKey: supabaseConfig.serviceRoleKey ? \"✓ Set\" : \"✗ Missing\",\n  });\n  process.exit(1);\n}\n\n// Log configuration status\nconsole.log(\"✓ Supabase configuration loaded successfully\");\nconsole.log(\"✓ URL:\", supabaseConfig.url ? \"Configured\" : \"Missing\");\nconsole.log(\n  \"✓ Service Role Key:\",\n  supabaseConfig.serviceRoleKey ? \"Configured\" : \"Missing\"\n);\nconsole.log(\n  \"✓ Database Password:\",\n  supabaseConfig.dbPassword\n    ? \"Configured\"\n    : \"Optional - using service role key authentication\"\n);\n"}