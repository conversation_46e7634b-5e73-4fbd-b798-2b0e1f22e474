{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/credentialsRoutes.ts"}, "originalCode": "import express, { Request, Response } from \"express\";\nimport { supabaseClient } from \"../middleware/supabaseMiddleware\";\nimport { z } from \"zod\";\nimport { \n  storeUser<PERSON><PERSON><PERSON><PERSON>, \n  getUserApi<PERSON>ey, \n  deleteUserApi<PERSON>ey,\n  getEphemeralUserCredentials \n} from \"../middleware/apiKeyStorage\";\n\nconst router = express.Router();\n\n// Validation schemas\nconst storeCredentialsSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n  baseUrl: z.string().url(\"Valid base URL is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\")\n});\n\nconst getCredentialsSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\")\n});\n\n// Helper function to verify the current user is authenticated\nasync function getAuthenticatedUser(\n  req: Request\n): Promise<\n  | { error: string; details?: string; status: number }\n  | { user: { id: string; [key: string]: any } }\n> {\n  try {\n    const authHeader = req.headers.authorization;\n\n    if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n      return { error: \"Unauthorized: Missing or malformed token\", status: 401 };\n    }\n\n    const token = authHeader.split(\" \")[1];\n    if (!token) {\n      return { error: \"Unauthorized: Missing token\", status: 401 };\n    }\n\n    const supabase = supabaseClient;\n    const { data, error: getUserError } = await supabase.auth.getUser(token);\n\n    if (getUserError) {\n      if (\n        getUserError.message.toLowerCase().includes(\"invalid token\") ||\n        getUserError.message.includes(\"jwt\")\n      ) {\n        return {\n          error: \"Unauthorized: Invalid token\",\n          details: getUserError.message,\n          status: 401,\n        };\n      }\n      return {\n        error: \"Server error validating token\",\n        details: getUserError.message,\n        status: 500,\n      };\n    }\n\n    const user = data?.user;\n    if (!user) {\n      return { error: \"Unauthorized: No user found for token\", status: 401 };\n    }\n\n    if (!user.id) {\n      return { error: \"User ID missing from authenticated user\", status: 500 };\n    }\n\n    return { user: { ...user } };\n  } catch (err: any) {\n    console.error(\"Auth error in credentialsRoutes:\", err.message, err.stack);\n    return { error: \"Authentication error\", status: 500 };\n  }\n}\n\n// POST /api/credentials - Store user's AI provider credentials securely\nrouter.post(\"/\", async (req: Request, res: Response) => {\n  const authResult = await getAuthenticatedUser(req);\n  if (\"error\" in authResult) {\n    return res\n      .status(authResult.status)\n      .json({ error: authResult.error, details: authResult.details });\n  }\n  const user = authResult.user;\n\n  try {\n    const validationResult = storeCredentialsSchema.safeParse(req.body);\n    if (!validationResult.success) {\n      return res\n        .status(400)\n        .json({ \n          error: \"Invalid request data\", \n          details: validationResult.error.flatten() \n        });\n    }\n\n    const { provider, apiKey, baseUrl, extractionModel, generationModel } = validationResult.data;\n\n    const result = await storeUserApiKey(\n      user.id,\n      provider,\n      apiKey,\n      baseUrl,\n      { extraction: extractionModel, generation: generationModel }\n    );\n\n    if (!result.success) {\n      return res.status(500).json({ \n        error: \"Failed to store credentials\", \n        details: result.error \n      });\n    }\n\n    return res.status(200).json({ \n      success: true, \n      message: \"Credentials stored securely\" \n    });\n  } catch (error: any) {\n    console.error(\"Error storing credentials:\", error);\n    return res\n      .status(500)\n      .json({ error: \"Failed to store credentials\", details: error.message });\n  }\n});\n\n// GET /api/credentials/:provider - Get user's stored credentials (without API key)\nrouter.get(\"/:provider\", async (req: Request, res: Response) => {\n  const authResult = await getAuthenticatedUser(req);\n  if (\"error\" in authResult) {\n    return res\n      .status(authResult.status)\n      .json({ error: authResult.error, details: authResult.details });\n  }\n  const user = authResult.user;\n\n  try {\n    const { provider } = req.params;\n    \n    if (!provider) {\n      return res.status(400).json({ error: \"Provider parameter is required\" });\n    }\n\n    const result = await getUserApiKey(user.id, provider);\n\n    if (!result.success) {\n      if (result.error === 'No credentials found for this provider') {\n        return res.status(404).json({ \n          error: \"No credentials found\", \n          hasCredentials: false \n        });\n      }\n      return res.status(500).json({ \n        error: \"Failed to retrieve credentials\", \n        details: result.error \n      });\n    }\n\n    // Return configuration without the actual API key\n    return res.status(200).json({\n      success: true,\n      hasCredentials: true,\n      configuration: {\n        provider,\n        baseUrl: result.credentials!.baseUrl,\n        extractionModel: result.credentials!.extractionModel,\n        generationModel: result.credentials!.generationModel\n      }\n    });\n  } catch (error: any) {\n    console.error(\"Error retrieving credentials:\", error);\n    return res\n      .status(500)\n      .json({ error: \"Failed to retrieve credentials\", details: error.message });\n  }\n});\n\n// DELETE /api/credentials/:provider - Delete user's stored credentials\nrouter.delete(\"/:provider\", async (req: Request, res: Response) => {\n  const authResult = await getAuthenticatedUser(req);\n  if (\"error\" in authResult) {\n    return res\n      .status(authResult.status)\n      .json({ error: authResult.error, details: authResult.details });\n  }\n  const user = authResult.user;\n\n  try {\n    const { provider } = req.params;\n    \n    if (!provider) {\n      return res.status(400).json({ error: \"Provider parameter is required\" });\n    }\n\n    const result = await deleteUserApiKey(user.id, provider);\n\n    if (!result.success) {\n      return res.status(500).json({ \n        error: \"Failed to delete credentials\", \n        details: result.error \n      });\n    }\n\n    return res.status(200).json({ \n      success: true, \n      message: \"Credentials deleted successfully\" \n    });\n  } catch (error: any) {\n    console.error(\"Error deleting credentials:\", error);\n    return res\n      .status(500)\n      .json({ error: \"Failed to delete credentials\", details: error.message });\n  }\n});\n\n// Internal endpoint for getting ephemeral credentials (used by AI routes)\n// This should not be exposed publicly - only used internally by other routes\nexport async function getEphemeralCredentialsForUser(\n  userId: string, \n  provider: string\n) {\n  return getEphemeralUserCredentials(userId, provider);\n}\n\nexport default router;\n", "modifiedCode": "import express, { Request, Response } from \"express\";\nimport { supabaseClient } from \"../middleware/supabaseMiddleware\";\nimport { z } from \"zod\";\nimport { \n  storeUser<PERSON><PERSON><PERSON><PERSON>, \n  getUserApi<PERSON>ey, \n  deleteUserApi<PERSON>ey,\n  getEphemeralUserCredentials \n} from \"../middleware/apiKeyStorage\";\n\nconst router = express.Router();\n\n// Validation schemas\nconst storeCredentialsSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n  baseUrl: z.string().url(\"Valid base URL is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\")\n});\n\nconst getCredentialsSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\")\n});\n\n// Helper function to verify the current user is authenticated\nasync function getAuthenticatedUser(\n  req: Request\n): Promise<\n  | { error: string; details?: string; status: number }\n  | { user: { id: string; [key: string]: any } }\n> {\n  try {\n    const authHeader = req.headers.authorization;\n\n    if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n      return { error: \"Unauthorized: Missing or malformed token\", status: 401 };\n    }\n\n    const token = authHeader.split(\" \")[1];\n    if (!token) {\n      return { error: \"Unauthorized: Missing token\", status: 401 };\n    }\n\n    const supabase = supabaseClient;\n    const { data, error: getUserError } = await supabase.auth.getUser(token);\n\n    if (getUserError) {\n      if (\n        getUserError.message.toLowerCase().includes(\"invalid token\") ||\n        getUserError.message.includes(\"jwt\")\n      ) {\n        return {\n          error: \"Unauthorized: Invalid token\",\n          details: getUserError.message,\n          status: 401,\n        };\n      }\n      return {\n        error: \"Server error validating token\",\n        details: getUserError.message,\n        status: 500,\n      };\n    }\n\n    const user = data?.user;\n    if (!user) {\n      return { error: \"Unauthorized: No user found for token\", status: 401 };\n    }\n\n    if (!user.id) {\n      return { error: \"User ID missing from authenticated user\", status: 500 };\n    }\n\n    return { user: { ...user } };\n  } catch (err: any) {\n    console.error(\"Auth error in credentialsRoutes:\", err.message, err.stack);\n    return { error: \"Authentication error\", status: 500 };\n  }\n}\n\n// POST /api/credentials - Store user's AI provider credentials securely\nrouter.post(\"/\", async (req: Request, res: Response) => {\n  const authResult = await getAuthenticatedUser(req);\n  if (\"error\" in authResult) {\n    return res\n      .status(authResult.status)\n      .json({ error: authResult.error, details: authResult.details });\n  }\n  const user = authResult.user;\n\n  try {\n    const validationResult = storeCredentialsSchema.safeParse(req.body);\n    if (!validationResult.success) {\n      return res\n        .status(400)\n        .json({ \n          error: \"Invalid request data\", \n          details: validationResult.error.flatten() \n        });\n    }\n\n    const { provider, apiKey, baseUrl, extractionModel, generationModel } = validationResult.data;\n\n    const result = await storeUserApiKey(\n      user.id,\n      provider,\n      apiKey,\n      baseUrl,\n      { extraction: extractionModel, generation: generationModel }\n    );\n\n    if (!result.success) {\n      return res.status(500).json({ \n        error: \"Failed to store credentials\", \n        details: result.error \n      });\n    }\n\n    return res.status(200).json({ \n      success: true, \n      message: \"Credentials stored securely\" \n    });\n  } catch (error: any) {\n    console.error(\"Error storing credentials:\", error);\n    return res\n      .status(500)\n      .json({ error: \"Failed to store credentials\", details: error.message });\n  }\n});\n\n// GET /api/credentials/:provider - Get user's stored credentials (without API key)\nrouter.get(\"/:provider\", async (req: Request, res: Response) => {\n  const authResult = await getAuthenticatedUser(req);\n  if (\"error\" in authResult) {\n    return res\n      .status(authResult.status)\n      .json({ error: authResult.error, details: authResult.details });\n  }\n  const user = authResult.user;\n\n  try {\n    const { provider } = req.params;\n    \n    if (!provider) {\n      return res.status(400).json({ error: \"Provider parameter is required\" });\n    }\n\n    const result = await getUserApiKey(user.id, provider);\n\n    if (!result.success) {\n      if (result.error === 'No credentials found for this provider') {\n        return res.status(404).json({ \n          error: \"No credentials found\", \n          hasCredentials: false \n        });\n      }\n      return res.status(500).json({ \n        error: \"Failed to retrieve credentials\", \n        details: result.error \n      });\n    }\n\n    // Return configuration without the actual API key\n    return res.status(200).json({\n      success: true,\n      hasCredentials: true,\n      configuration: {\n        provider,\n        baseUrl: result.credentials!.baseUrl,\n        extractionModel: result.credentials!.extractionModel,\n        generationModel: result.credentials!.generationModel\n      }\n    });\n  } catch (error: any) {\n    console.error(\"Error retrieving credentials:\", error);\n    return res\n      .status(500)\n      .json({ error: \"Failed to retrieve credentials\", details: error.message });\n  }\n});\n\n// DELETE /api/credentials/:provider - Delete user's stored credentials\nrouter.delete(\"/:provider\", async (req: Request, res: Response) => {\n  const authResult = await getAuthenticatedUser(req);\n  if (\"error\" in authResult) {\n    return res\n      .status(authResult.status)\n      .json({ error: authResult.error, details: authResult.details });\n  }\n  const user = authResult.user;\n\n  try {\n    const { provider } = req.params;\n    \n    if (!provider) {\n      return res.status(400).json({ error: \"Provider parameter is required\" });\n    }\n\n    const result = await deleteUserApiKey(user.id, provider);\n\n    if (!result.success) {\n      return res.status(500).json({ \n        error: \"Failed to delete credentials\", \n        details: result.error \n      });\n    }\n\n    return res.status(200).json({ \n      success: true, \n      message: \"Credentials deleted successfully\" \n    });\n  } catch (error: any) {\n    console.error(\"Error deleting credentials:\", error);\n    return res\n      .status(500)\n      .json({ error: \"Failed to delete credentials\", details: error.message });\n  }\n});\n\n// Internal endpoint for getting ephemeral credentials (used by AI routes)\n// This should not be exposed publicly - only used internally by other routes\nexport async function getEphemeralCredentialsForUser(\n  userId: string, \n  provider: string\n) {\n  return getEphemeralUserCredentials(userId, provider);\n}\n\nexport default router;\n"}