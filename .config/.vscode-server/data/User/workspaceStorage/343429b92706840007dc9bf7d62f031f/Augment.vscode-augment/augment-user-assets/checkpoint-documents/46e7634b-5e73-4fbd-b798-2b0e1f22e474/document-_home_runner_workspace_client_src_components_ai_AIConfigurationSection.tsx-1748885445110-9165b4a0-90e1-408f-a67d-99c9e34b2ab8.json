{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ai/AIConfigurationSection.tsx"}, "originalCode": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash-preview-05-20\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = (values: AIConfigFormValues) => {\n    // Set the legacy 'model' field to match generationModel for backward compatibility\n    const settingsToSave = {\n      ...values,\n      model: values.generationModel,\n    };\n\n    setAIProviderSettings(settingsToSave);\n    toast({\n      title: \"AI Provider Configured\",\n      description: \"Your AI settings have been saved successfully.\",\n    });\n    navigate(\"/\");\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash 2.5 for extraction and Pro 2.5 for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is used securely and never stored\n                      permanently.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is sent securely to our backend and used only\n                    for your current session. We don't store your credentials\n                    permanently. All AI requests are processed through your\n                    configured provider using your own API key, so you maintain\n                    control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                Save Configuration\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n", "modifiedCode": "import React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useLocation } from \"wouter\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  getAIProviderSettings,\n  setAIProviderSettings,\n  getAvailableProviders,\n  getAvailableModels,\n  getDefaultBaseUrl,\n} from \"@/lib/ai-provider\";\nimport { storeCredentialsAPI } from \"@/lib/api\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  Shield,\n  Eye,\n  EyeOff,\n  Sparkles,\n  Zap,\n  Brain,\n  Loader2,\n} from \"lucide-react\";\n\nconst aiConfigSchema = z.object({\n  provider: z.string().min(1, \"Provider is required\"),\n  extractionModel: z.string().min(1, \"Extraction model is required\"),\n  generationModel: z.string().min(1, \"Generation model is required\"),\n  baseUrl: z.string().url(\"Must be a valid URL\"),\n  apiKey: z.string().min(1, \"API key is required\"),\n});\n\ntype AIConfigFormValues = z.infer<typeof aiConfigSchema>;\n\nconst AIConfigurationSection: React.FC = () => {\n  const [, navigate] = useLocation();\n  const { toast } = useToast();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const availableProviders = getAvailableProviders();\n  const defaultSettings = getAIProviderSettings();\n  const [selectedProvider, setSelectedProvider] = useState(\n    defaultSettings.provider\n  );\n  const availableModels = getAvailableModels(selectedProvider);\n\n  const form = useForm<AIConfigFormValues>({\n    resolver: zodResolver(aiConfigSchema),\n    defaultValues: {\n      provider: defaultSettings.provider,\n      extractionModel: defaultSettings.extractionModel,\n      generationModel: defaultSettings.generationModel,\n      baseUrl: defaultSettings.baseUrl,\n      apiKey: defaultSettings.apiKey,\n    },\n  });\n\n  const handleProviderChange = (value: string) => {\n    setSelectedProvider(value);\n    const baseUrl = getDefaultBaseUrl(value);\n    form.setValue(\"baseUrl\", baseUrl);\n\n    // Set first available model for this provider\n    const models = getAvailableModels(value);\n    if (models.length > 0) {\n      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5\n      const extractionModel =\n        models.find((m) => m.id === \"google/gemini-2.5-flash-preview-05-20\")?.id ||\n        models[0].id;\n      const generationModel =\n        models.find((m) => m.id === \"google/gemini-2.5-pro-preview\")?.id ||\n        models[0].id;\n\n      form.setValue(\"extractionModel\", extractionModel);\n      form.setValue(\"generationModel\", generationModel);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowApiKey(!showApiKey);\n  };\n\n  const onSubmit = async (values: AIConfigFormValues) => {\n    setIsSubmitting(true);\n\n    try {\n      // First, validate the API key is provided\n      if (!values.apiKey.trim()) {\n        toast({\n          title: \"API Key Required\",\n          description: \"Please enter your API key to save the configuration.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Store credentials securely on the backend\n      await storeCredentialsAPI({\n        provider: values.provider,\n        apiKey: values.apiKey,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n      });\n\n      // Store non-sensitive settings in localStorage for quick access\n      const settingsToSave = {\n        provider: values.provider,\n        baseUrl: values.baseUrl,\n        extractionModel: values.extractionModel,\n        generationModel: values.generationModel,\n        model: values.generationModel, // Legacy field for backward compatibility\n      };\n      setAIProviderSettings(settingsToSave);\n\n      toast({\n        title: \"AI Provider Configured\",\n        description: \"Your API credentials have been saved securely and your settings are now active.\",\n      });\n\n      navigate(\"/\");\n    } catch (error) {\n      console.error(\"Failed to save AI configuration:\", error);\n      toast({\n        title: \"Configuration Save Failed\",\n        description: error instanceof Error\n          ? error.message\n          : \"Failed to save your AI configuration. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700\">\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle className=\"flex items-center gap-2 text-2xl text-purple-400\">\n          <Settings className=\"h-6 w-6\" /> AI Configuration\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-6 space-y-6\">\n        <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n          <p className=\"text-slate-200\">\n            Configure the AI provider that will power your study tools. Your API\n            credentials are sent securely to the backend and used only for the\n            current request.\n          </p>\n          <div className=\"mt-2 text-xs text-slate-300 flex items-center gap-2\">\n            <Sparkles className=\"h-4 w-4 text-purple-400\" />\n            <span>\n              We highly recommend OpenRouter with Gemini 2.5\n              models - Flash 2.5 for extraction and Pro 2.5 for generation.\n            </span>\n          </div>\n        </div>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            <FormField\n              control={form.control}\n              name=\"provider\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel className=\"text-slate-300 font-medium\">\n                    API Provider\n                  </FormLabel>\n                  <Select\n                    onValueChange={(value) => {\n                      field.onChange(value);\n                      handleProviderChange(value);\n                    }}\n                    defaultValue={field.value}\n                  >\n                    <FormControl>\n                      <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                        <SelectValue placeholder=\"Select API provider\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                      {availableProviders.map((provider) => (\n                        <SelectItem key={provider} value={provider}>\n                          {provider}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormDescription className=\"text-slate-400\">\n                    {field.value === \"OpenRouter\" &&\n                      \"Recommended for best compatibility with Gemini models\"}\n                  </FormDescription>\n                  <FormMessage className=\"text-red-400\" />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg\">\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Zap className=\"h-4 w-4\" /> Extraction Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"extractionModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Text Extraction\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select extraction model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`extraction-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        Fast model for document processing\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"flex items-center gap-2 text-purple-400 font-medium mb-4\">\n                  <Brain className=\"h-4 w-4\" /> Generation Model\n                </h3>\n                <FormField\n                  control={form.control}\n                  name=\"generationModel\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"text-slate-300\">\n                        Content Generation\n                      </FormLabel>\n                      <Select\n                        onValueChange={field.onChange}\n                        defaultValue={field.value}\n                      >\n                        <FormControl>\n                          <SelectTrigger className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                            <SelectValue placeholder=\"Select generation model\" />\n                          </SelectTrigger>\n                        </FormControl>\n                        <SelectContent className=\"bg-slate-900 border-slate-700 text-slate-200\">\n                          {availableModels.map((model) => (\n                            <SelectItem\n                              key={`generation-${model.id}`}\n                              value={model.id}\n                            >\n                              {model.name}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                      <FormDescription className=\"text-slate-400\">\n                        High-quality model for flashcard creation\n                      </FormDescription>\n                      <FormMessage className=\"text-red-400\" />\n                    </FormItem>\n                  )}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <FormField\n                control={form.control}\n                name=\"baseUrl\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Base URL\n                    </FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"https://api.example.com\"\n                        {...field}\n                        className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                      />\n                    </FormControl>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"apiKey\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"text-slate-300 font-medium\">\n                      API Key\n                    </FormLabel>\n                    <div className=\"relative\">\n                      <FormControl>\n                        <Input\n                          type={showApiKey ? \"text\" : \"password\"}\n                          placeholder=\"Enter your API key\"\n                          {...field}\n                          className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                        />\n                      </FormControl>\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300\"\n                        onClick={togglePasswordVisibility}\n                      >\n                        {showApiKey ? (\n                          <EyeOff className=\"h-4 w-4\" />\n                        ) : (\n                          <Eye className=\"h-4 w-4\" />\n                        )}\n                      </Button>\n                    </div>\n                    <FormDescription className=\"text-slate-400\">\n                      Your API key is used securely and never stored\n                      permanently.\n                    </FormDescription>\n                    <FormMessage className=\"text-red-400\" />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n              <div className=\"flex items-start gap-3\">\n                <Shield className=\"h-5 w-5 text-purple-400 mt-0.5\" />\n                <div>\n                  <p className=\"text-slate-200 font-medium\">\n                    Security Information\n                  </p>\n                  <p className=\"text-slate-300 text-sm mt-1\">\n                    Your API key is sent securely to our backend and used only\n                    for your current session. We don't store your credentials\n                    permanently. All AI requests are processed through your\n                    configured provider using your own API key, so you maintain\n                    control of usage and costs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => navigate(\"/\")}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n                disabled={isSubmitting}\n              >\n                Save Configuration\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AIConfigurationSection;\n"}