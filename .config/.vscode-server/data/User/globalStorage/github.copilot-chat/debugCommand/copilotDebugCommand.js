"use strict";var q=Object.create;var u=Object.defineProperty;var M=Object.getOwnPropertyDescriptor;var I=Object.getOwnPropertyNames;var N=Object.getPrototypeOf,$=Object.prototype.hasOwnProperty;var F=(i,r,e,a)=>{if(r&&typeof r=="object"||typeof r=="function")for(let n of I(r))!$.call(i,n)&&n!==e&&u(i,n,{get:()=>r[n],enumerable:!(a=M(r,n))||a.enumerable});return i};var g=(i,r,e)=>(e=i!=null?q(N(i)):{},F(r||!i||!i.__esModule?u(e,"default",{value:i,enumerable:!0}):e,i));var S=require("crypto"),E=require("node:net"),R=require("os"),C=g(require("path")),h=g(require("readline"));var w=require("child_process"),v=(i,r)=>{let e,a=!1,n=[r];if(i){let[t,...s]=i.split(" ");e=t,n=[...s,r]}else switch(process.platform){case"win32":e="cmd",a=!0,n=["/c","start",'""',`"${r}"`];break;case"darwin":e="open";break;case"linux":default:e="xdg-open";break}return new Promise((t,s)=>{let o="",l=(0,w.spawn)(e,n,{stdio:"pipe",shell:a,env:{...process.env,ELECTRON_RUN_AS_NODE:void 0}});l.stdout.setEncoding("utf8").on("data",d=>o+=d),l.stderr.setEncoding("utf8").on("data",d=>o+=d),l.on("error",s),l.on("exit",d=>{d!==0?s(new Error(`Failed to open: ${o}`)):t()})})};var y=require("stream"),f=class extends y.Transform{constructor(e){super();this.prefix=[];this.splitSuffix=Buffer.alloc(0);if(typeof e=="string"&&e.length===1)this.splitter=e.charCodeAt(0);else if(typeof e=="number")this.splitter=e;else throw new Error("not implemented here")}_transform(e,a,n){let t=0;for(;t<e.length;){let s=e.indexOf(this.splitter,t);if(s===-1)break;let o=e.subarray(t,s),l=this.prefix.length||this.splitSuffix.length?Buffer.concat([...this.prefix,o,this.splitSuffix]):o;this.push(l),this.prefix.length=0,t=s+1}t<e.length&&this.prefix.push(e.subarray(t)),n()}_flush(e){this.prefix.length&&this.push(Buffer.concat([...this.prefix,this.splitSuffix])),e()}};var x=process.platform==="win32"?`\r
`:`
`,m=class{constructor(r){this.stream=r;this.methods=new Map;this.pendingRequests=new Map;this.idCounter=0,this.stream.pipe(new f(`
`)).on("data",e=>this.handleData(e)),this.ended=new Promise(e=>this.stream.on("end",()=>{this.didEnd=!0,e()}))}registerMethod(r,e){this.methods.set(r,e)}async callMethod(r,e){let a=this.idCounter++,n={id:a,method:r,params:e},t=new Promise((s,o)=>{this.pendingRequests.set(a,{resolve:s,reject:o})});return this.stream.write(JSON.stringify(n)+x),Promise.race([t,this.ended])}dispose(){this.didEnd=!0,this.stream.end();for(let{reject:r}of this.pendingRequests.values())r(new Error("RPC connection closed"));this.pendingRequests.clear()}async handleData(r){let e=JSON.parse(r.toString());if("method"in e){let{id:a,method:n,params:t}=e,s={id:a};try{if(this.methods.has(n)){let o=await this.methods.get(n)(t);s.result=o}else throw new Error(`Method not found: ${n}`)}catch(o){s.error={code:-1,message:String(o.stack||o)}}this.didEnd||this.stream.write(JSON.stringify(s)+x)}else{let{id:a,result:n,error:t}=e,s=this.pendingRequests.get(a);this.pendingRequests.delete(a),t!==void 0?s?.reject(new Error(t.message)):s?.resolve(n)}}};var[J,G,_,B,...p]=process.argv;var c={"--print":!1,"--no-cache":!1,"--help":!1,"--save":!1,"--once":!1};for(;p.length&&c.hasOwnProperty(p[0]);)c[p.shift()]=!0;(!p.length||c["--help"])&&(console.log(`Usage: copilot-debug [${Object.keys(c).join("] [")}] <command> <args...>`),console.log(""),console.log("Options:"),console.log("  --print     Print the generated configuration without running it"),console.log("  --no-cache  Generate a new configuration without checking the cache."),console.log("  --save      Save the configuration to your launch.json."),console.log("  --once      Exit after the debug session ends."),console.log("  --help      Print this help."),process.exit(c["--help"]?0:1));var b=h.createInterface({input:process.stdin,output:process.stdout});h.emitKeypressEvents(process.stdin);process.stdin.setRawMode(!0);var V=(0,E.createServer)(i=>{clearInterval(k);let r=new m(i);r.registerMethod("output",({category:t,output:s})=>(t==="stderr"?process.stderr.write(s):t==="stdout"?process.stdout.write(s):t!=="telemetry"&&s&&console.log(s),Promise.resolve())),r.registerMethod("exit",async({code:t,error:s})=>{s&&!e&&console.error(s),await Promise.all([new Promise(o=>process.stdout.end(o)),new Promise(o=>process.stderr.end(o))]).then(()=>process.exit(t))});let e=!1;function a(){e?process.exit(1):(e=!0,i.end(()=>{process.exit(1)}))}process.on("SIGINT",a),process.stdin.on("keypress",(t,s)=>{(s.sequence===""||s.name==="c"&&(s.ctrl||s.meta))&&a()}),r.registerMethod("question",t=>new Promise(s=>{if(t.singleKey){console.log(t.message);let o=l=>{l&&(process.stdout.write("\b"),process.stdin.off("keypress",o),s(l===`
`||l==="\r"?"Enter":l?.toUpperCase()||""))};process.stdin.on("keypress",o)}else b.question(`${t.message} [${t.defaultValue}] `,s)})),r.registerMethod("confirm",t=>new Promise(s=>{b.question(`${t.message} [${t.defaultValue?"Y/n":"y/N"}] `,o=>{s(o===""?t.defaultValue:o.toLowerCase()[0]==="y")})}));let n={cwd:process.cwd(),args:p,forceNew:c["--no-cache"],printOnly:c["--print"],save:c["--save"],once:c["--once"]};r.callMethod("start",n)}),k=setInterval(()=>{console.log("> Waiting for VS Code to connect...")},2e3),O=`copilot-dbg.${process.pid}-${(0,S.randomBytes)(4).toString("hex")}.sock`,P=C.join(process.platform==="win32"?"\\\\.\\pipe\\":(0,R.tmpdir)(),O);V.listen(P,()=>{v(B,_+(process.platform==="win32"?`/${O}`:P)).then(()=>{},i=>{console.error("Failed to open the activation URI:",i),process.exit(1)})});
