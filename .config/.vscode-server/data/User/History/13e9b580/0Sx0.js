// Simple test script to verify document content endpoint
// Run with: node test-document-endpoint.js

async function testDocumentEndpoint() {
  try {
    console.log('🧪 Testing Document Content Endpoint...');
    
    // Test health endpoint first
    console.log('\n1. Testing health endpoint...');
    const healthResponse = await fetch('http://localhost:5000/api/health');
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData.status);
    
    // Test document endpoint without auth (should fail)
    console.log('\n2. Testing document endpoint without auth...');
    const noAuthResponse = await fetch('http://localhost:5000/api/documents/test-id/content');
    console.log('📄 Response status:', noAuthResponse.status);
    console.log('📄 Expected: 401 (Unauthorized)');
    
    if (noAuthResponse.status === 401) {
      console.log('✅ Authentication protection working correctly');
    } else {
      console.log('❌ Authentication protection may not be working');
    }
    
    // Test with invalid document ID but valid structure
    console.log('\n3. Testing endpoint structure...');
    const structureResponse = await fetch('http://localhost:5000/api/documents/invalid-id/content', {
      headers: {
        'Authorization': 'Bearer invalid-token'
      }
    });
    console.log('📄 Response status:', structureResponse.status);
    console.log('📄 Expected: 401 or 404');
    
    console.log('\n✅ Document endpoint tests completed');
    console.log('\n📋 Summary:');
    console.log('- Health endpoint: Working');
    console.log('- Authentication: Protected');
    console.log('- Endpoint structure: Accessible');
    console.log('\n💡 To test with real documents, use the frontend with proper authentication.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n🔧 Make sure the server is running with: npm run dev:server');
  }
}

testDocumentEndpoint();
