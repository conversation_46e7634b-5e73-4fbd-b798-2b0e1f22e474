import { createClient } from '@supabase/supabase-js';
import { supabaseConfig } from '../config';
import crypto from 'crypto';

// Create Supabase client for API key operations
const supabase = createClient(supabaseConfig.url, supabaseConfig.serviceRoleKey);

// Encryption key for API keys (should be from environment variable)
const ENCRYPTION_KEY = process.env.API_KEY_ENCRYPTION_KEY || crypto.randomBytes(32);
const ALGORITHM = 'aes-256-gcm';

/**
 * Encrypt an API key for secure storage
 */
function encryptApiKey(apiKey: string): { encrypted: string; iv: string; tag: string } {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(ALGORITHM, ENCRYPTION_KEY);
  cipher.setAAD(iv); // Use IV as additional authenticated data

  let encrypted = cipher.update(apiKey, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  const tag = cipher.getAuthTag();

  return {
    encrypted,
    iv: iv.toString('hex'),
    tag: tag.toString('hex')
  };
}

/**
 * Decrypt an API key from storage
 */
function decryptApiKey(encryptedData: { encrypted: string; iv: string; tag: string }): string {
  const iv = Buffer.from(encryptedData.iv, 'hex');
  const decipher = crypto.createDecipher(ALGORITHM, ENCRYPTION_KEY);
  decipher.setAAD(iv); // Use IV as additional authenticated data
  decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'));

  let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
}

/**
 * Store user's AI provider credentials securely in database
 */
export async function storeUserApiKey(
  userId: string, 
  provider: string, 
  apiKey: string, 
  baseUrl: string,
  models: { extraction: string; generation: string }
): Promise<{ success: boolean; error?: string }> {
  try {
    // Encrypt the API key
    const encryptedKey = encryptApiKey(apiKey);
    
    // Store in database with encryption metadata
    const { error } = await supabase
      .from('user_ai_credentials')
      .upsert({
        user_id: userId,
        provider,
        encrypted_api_key: encryptedKey.encrypted,
        encryption_iv: encryptedKey.iv,
        encryption_tag: encryptedKey.tag,
        base_url: baseUrl,
        extraction_model: models.extraction,
        generation_model: models.generation,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id,provider'
      });

    if (error) {
      console.error('Error storing user API key:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in storeUserApiKey:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Retrieve and decrypt user's AI provider credentials
 */
export async function getUserApiKey(
  userId: string, 
  provider: string
): Promise<{ 
  success: boolean; 
  credentials?: {
    apiKey: string;
    baseUrl: string;
    extractionModel: string;
    generationModel: string;
  };
  error?: string;
}> {
  try {
    const { data, error } = await supabase
      .from('user_ai_credentials')
      .select('*')
      .eq('user_id', userId)
      .eq('provider', provider)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No credentials found
        return { success: false, error: 'No credentials found for this provider' };
      }
      console.error('Error retrieving user API key:', error);
      return { success: false, error: error.message };
    }

    // Decrypt the API key
    const decryptedApiKey = decryptApiKey({
      encrypted: data.encrypted_api_key,
      iv: data.encryption_iv,
      tag: data.encryption_tag
    });

    return {
      success: true,
      credentials: {
        apiKey: decryptedApiKey,
        baseUrl: data.base_url,
        extractionModel: data.extraction_model,
        generationModel: data.generation_model
      }
    };
  } catch (error: any) {
    console.error('Error in getUserApiKey:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Delete user's stored API credentials
 */
export async function deleteUserApiKey(
  userId: string, 
  provider: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('user_ai_credentials')
      .delete()
      .eq('user_id', userId)
      .eq('provider', provider);

    if (error) {
      console.error('Error deleting user API key:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in deleteUserApiKey:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Get user credentials for ephemeral use (for AI API calls)
 * This function should be used only during AI API requests
 */
export async function getEphemeralUserCredentials(
  userId: string,
  provider: string
): Promise<{
  success: boolean;
  credentials?: {
    apiKey: string;
    baseUrl: string;
    extractionModel: string;
    generationModel: string;
  };
  error?: string;
}> {
  // This is the same as getUserApiKey but with explicit naming
  // to emphasize ephemeral usage
  return getUserApiKey(userId, provider);
}
