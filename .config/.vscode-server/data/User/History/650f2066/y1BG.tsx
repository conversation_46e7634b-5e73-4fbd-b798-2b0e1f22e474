import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import Spinner from "@/components/ui/Spinner";

interface FlashcardGenerationOptions {
  numberOfCards: number;
  customPrompt: string;
}

interface FlashcardGenerationPopupProps {
  trigger: React.ReactNode;
  onGenerate: (options: FlashcardGenerationOptions) => Promise<void>;
  isGenerating: boolean;
  disabled?: boolean;
  maxCards?: number;
}

export const FlashcardGenerationPopup: React.FC<FlashcardGenerationPopupProps> = ({
  trigger,
  onGenerate,
  isGenerating,
  disabled = false,
  maxCards = 50,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [options, setOptions] = useState<FlashcardGenerationOptions>({
    numberOfCards: 10,
    customPrompt: "",
  });

  const handleGenerate = async () => {
    if (options.numberOfCards <= 0) {
      return; // Validation handled by UI
    }

    try {
      await onGenerate(options);
      setIsOpen(false);
    } catch (error) {
      // Error handling is done by parent component
      console.error("Flashcard generation failed:", error);
    }
  };

  const isValidOptions = options.numberOfCards > 0 && options.numberOfCards <= maxCards;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild disabled={disabled}>
        {trigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] bg-card border-border">
        <DialogHeader>
          <DialogTitle className="text-card-foreground">Generate Flashcards Options</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          {/* Number of Cards */}
          <div className="space-y-2">
            <Label htmlFor="numberOfCards" className="text-card-foreground">
              Number of Flashcards
            </Label>
            <Input
              id="numberOfCards"
              type="number"
              min="1"
              max={maxCards}
              value={options.numberOfCards}
              onChange={(e) =>
                setOptions((prev) => ({
                  ...prev,
                  numberOfCards: Math.max(1, Math.min(maxCards, parseInt(e.target.value) || 1)),
                }))
              }
              className="bg-background border-border text-foreground"
            />
            <p className="text-xs text-muted-foreground">
              Choose between 1 and {maxCards} flashcards
            </p>
          </div>

          {/* Custom Prompt */}
          <div className="space-y-2">
            <Label htmlFor="customPrompt" className="text-card-foreground">
              Custom Prompt (Optional)
            </Label>
            <Textarea
              id="customPrompt"
              placeholder="e.g., 'Focus on key definitions', 'Create scenario-based questions', 'Make flashcards suitable for beginners'"
              value={options.customPrompt}
              onChange={(e) =>
                setOptions((prev) => ({
                  ...prev,
                  customPrompt: e.target.value,
                }))
              }
              className="bg-background border-border text-foreground min-h-[80px]"
            />
            <p className="text-xs text-muted-foreground">
              Add specific instructions for the AI on what kind of flashcards you want.
            </p>
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isGenerating}
          >
            Cancel
          </Button>
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || !isValidOptions}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            {isGenerating ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Generating...
              </>
            ) : (
              "Generate Flashcards"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
