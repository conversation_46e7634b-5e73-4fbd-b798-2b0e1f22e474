# ChewyAI Security Documentation

## 🔐 Security Architecture

### API Key Management

#### Client-Side Security
- **NO API keys are stored on the client-side** (localStorage or sessionStorage)
- User AI credentials are stored encrypted in Supabase database
- Only non-sensitive configuration stored in localStorage (provider, models, base URL)
- Supabase anonymous key is safe for client-side use (read-only access)
- All sensitive operations require authentication

#### Backend Security
- User AI provider credentials encrypted with AES-256-GCM before database storage
- API keys retrieved and decrypted only during AI API calls (ephemeral use)
- No logging of sensitive credentials (API keys, tokens, headers)
- Backend-owned credentials managed via environment variables only
- Proper input validation and sanitization with Zod schemas
- Row Level Security (RLS) enforces user data isolation

### Authentication & Authorization

#### Supabase Authentication
- JWT-based authentication using Supabase Auth
- Row Level Security (RLS) policies enforce data access
- Session management handled by Supabase client
- Automatic token refresh

#### API Protection
- All protected endpoints require valid JWT token
- Authorization header: `Bearer <jwt_token>`
- User context extracted from token for data access
- Proper error handling for unauthorized requests

## 🛡️ Security Headers

### Production Security Headers
```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
Content-Security-Policy: [configured for app requirements]
```

### CORS Configuration
- Configured for specific origins in production
- Credentials allowed for authenticated requests
- Proper preflight handling for complex requests

## 🔒 Environment Variables

### Required Production Variables
```bash
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Client Environment Variables
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_API_BASE_URL=https://your-domain.com/api

# Application Configuration
NODE_ENV=production
PORT=80
```

### Security Best Practices
- Never commit `.env` files to version control
- Use different keys for development and production
- Rotate keys regularly
- Monitor for exposed credentials in logs

## 🚨 Data Protection

### User Data Handling
- Text extraction performed client-side when possible
- Content sent to self-hosted backend, then to user's AI provider
- User AI credentials encrypted and stored securely in database
- User data stored in Supabase with RLS policies enforcing isolation

### AI Provider Integration
- User's own API keys used for AI requests (retrieved from encrypted storage)
- No ChewyAI-owned AI credits exposed to users
- Transparent data flow: Client → ChewyAI Backend → User's AI Provider
- No caching of AI responses containing user data
- API keys decrypted only during request processing (ephemeral use)

## 🔍 Security Monitoring

### Logging Practices
- Log authentication attempts and failures
- Monitor API usage patterns
- Never log sensitive credentials or user data
- Implement proper error handling without exposing internals

### Health Checks
- `/api/health` endpoint for monitoring
- No sensitive information in health check responses
- Proper error responses without stack traces in production

## 🛠️ Development Security

### Local Development
- Use `.env.example` as template for local environment
- Never use production credentials in development
- Test with development Supabase project
- Validate security headers in development

### Code Security
- Input validation using Zod schemas
- SQL injection prevention with parameterized queries
- XSS prevention with proper output encoding
- CSRF protection for state-changing operations

## 📋 Security Checklist

### Pre-Deployment
- [ ] All environment variables configured
- [ ] No hardcoded credentials in code
- [ ] Security headers implemented
- [ ] CORS properly configured
- [ ] Authentication working correctly
- [ ] Input validation in place
- [ ] Error handling doesn't expose internals

### Post-Deployment
- [ ] Health check endpoint responding
- [ ] Authentication flow working
- [ ] API endpoints properly protected
- [ ] Security headers present in responses
- [ ] No sensitive data in logs
- [ ] HTTPS enforced (handled by Replit)

## 🚨 Incident Response

### If Credentials Are Compromised
1. Immediately rotate affected credentials
2. Update environment variables in deployment
3. Redeploy application
4. Monitor for unauthorized access
5. Notify users if necessary

### Security Updates
- Monitor dependencies for security vulnerabilities
- Update packages regularly
- Test security updates in development first
- Document security-related changes

## 🔍 Security Audit History

### 2025-06-02 - Comprehensive Security Audit
**Critical Vulnerabilities Found and Fixed:**

1. **CRITICAL: Hardcoded Production Credentials**
   - **Issue**: Supabase service role key hardcoded in `server/config.ts`
   - **Risk**: Full database access exposed in source code
   - **Fix**: Removed hardcoded fallback, enforced environment variables

2. **HIGH: Client-Side API Key Exposure**
   - **Issue**: Attempted to use `VITE_OPENROUTER_API_KEY` environment variable
   - **Risk**: API keys would be exposed in browser bundle
   - **Fix**: Removed client-side API key references

3. **HIGH: Insecure API Key Storage**
   - **Issue**: User API keys stored unencrypted in localStorage
   - **Risk**: Keys accessible to any script on domain
   - **Fix**: Implemented AES-256-GCM encryption with database storage

**Moderate Issues Fixed:**
- Removed sensitive header logging from middleware
- Updated npm packages to fix known vulnerabilities
- Enhanced authentication error handling

**New Security Features Implemented:**
- Encrypted user credential storage in database
- Row Level Security policies for credential access
- Secure API key retrieval for ephemeral use
- Enhanced environment variable validation

**Remaining Development Dependencies:**
- esbuild vulnerability affects development only (not production)
- Vite vulnerability affects development server only
- No impact on production deployment security
