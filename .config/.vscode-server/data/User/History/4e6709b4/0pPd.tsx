import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useLocation } from "wouter";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import {
  getAIProviderSettings,
  setAIProviderSettings,
  getAvailableProviders,
  getAvailableModels,
  getDefaultBaseUrl,
} from "@/lib/ai-provider";
import { storeCredentialsAPI } from "@/lib/api";
import {
  <PERSON><PERSON><PERSON>,
  Shield,
  Eye,
  EyeOff,
  Sparkles,
  Zap,
  Brain,
  Loader2,
} from "lucide-react";

const aiConfigSchema = z.object({
  provider: z.string().min(1, "Provider is required"),
  extractionModel: z.string().min(1, "Extraction model is required"),
  generationModel: z.string().min(1, "Generation model is required"),
  baseUrl: z.string().url("Must be a valid URL"),
  apiKey: z.string().min(1, "API key is required"),
});

type AIConfigFormValues = z.infer<typeof aiConfigSchema>;

const AIConfigurationSection: React.FC = () => {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const [showApiKey, setShowApiKey] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const availableProviders = getAvailableProviders();
  const defaultSettings = getAIProviderSettings();
  const [selectedProvider, setSelectedProvider] = useState(
    defaultSettings.provider
  );
  const availableModels = getAvailableModels(selectedProvider);

  const form = useForm<AIConfigFormValues>({
    resolver: zodResolver(aiConfigSchema),
    defaultValues: {
      provider: defaultSettings.provider,
      extractionModel: defaultSettings.extractionModel,
      generationModel: defaultSettings.generationModel,
      baseUrl: defaultSettings.baseUrl,
      apiKey: defaultSettings.apiKey,
    },
  });

  const handleProviderChange = (value: string) => {
    setSelectedProvider(value);
    const baseUrl = getDefaultBaseUrl(value);
    form.setValue("baseUrl", baseUrl);

    // Set first available model for this provider
    const models = getAvailableModels(value);
    if (models.length > 0) {
      // Set default extraction model to Flash 2.5 and generation model to Pro 2.5
      const extractionModel =
        models.find((m) => m.id === "google/gemini-2.5-flash-preview-05-20")?.id ||
        models[0].id;
      const generationModel =
        models.find((m) => m.id === "google/gemini-2.5-pro-preview")?.id ||
        models[0].id;

      form.setValue("extractionModel", extractionModel);
      form.setValue("generationModel", generationModel);
    }
  };

  const togglePasswordVisibility = () => {
    setShowApiKey(!showApiKey);
  };

  const onSubmit = async (values: AIConfigFormValues) => {
    setIsSubmitting(true);

    try {
      // First, validate the API key is provided
      if (!values.apiKey.trim()) {
        toast({
          title: "API Key Required",
          description: "Please enter your API key to save the configuration.",
          variant: "destructive",
        });
        return;
      }

      // Store credentials securely on the backend
      await storeCredentialsAPI({
        provider: values.provider,
        apiKey: values.apiKey,
        baseUrl: values.baseUrl,
        extractionModel: values.extractionModel,
        generationModel: values.generationModel,
      });

      // Store non-sensitive settings in localStorage for quick access
      const settingsToSave = {
        provider: values.provider,
        baseUrl: values.baseUrl,
        extractionModel: values.extractionModel,
        generationModel: values.generationModel,
        model: values.generationModel, // Legacy field for backward compatibility
      };
      setAIProviderSettings(settingsToSave);

      toast({
        title: "AI Provider Configured",
        description: "Your API credentials have been saved securely and your settings are now active.",
      });

      navigate("/");
    } catch (error) {
      console.error("Failed to save AI configuration:", error);
      toast({
        title: "Configuration Save Failed",
        description: error instanceof Error
          ? error.message
          : "Failed to save your AI configuration. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full shadow-lg bg-slate-800 text-slate-200 border border-slate-700">
      <CardHeader className="border-b border-slate-700">
        <CardTitle className="flex items-center gap-2 text-2xl text-purple-400">
          <Settings className="h-6 w-6" /> AI Configuration
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6 space-y-6">
        <div className="bg-slate-700 p-4 rounded-lg border border-slate-600">
          <p className="text-slate-200">
            Configure the AI provider that will power your study tools. Your API
            credentials are sent securely to the backend and used only for the
            current request.
          </p>
          <div className="mt-2 text-xs text-slate-300 flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-purple-400" />
            <span>
              We highly recommend OpenRouter with Gemini 2.5
              models - Flash 2.5 for extraction and Pro 2.5 for generation.
            </span>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="provider"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-300 font-medium">
                    API Provider
                  </FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      handleProviderChange(value);
                    }}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="bg-slate-900 border-slate-700 text-slate-200">
                        <SelectValue placeholder="Select API provider" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-slate-900 border-slate-700 text-slate-200">
                      {availableProviders.map((provider) => (
                        <SelectItem key={provider} value={provider}>
                          {provider}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription className="text-slate-400">
                    {field.value === "OpenRouter" &&
                      "Recommended for best compatibility with Gemini models"}
                  </FormDescription>
                  <FormMessage className="text-red-400" />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 border border-slate-700 bg-slate-800 p-4 rounded-lg">
              <div>
                <h3 className="flex items-center gap-2 text-purple-400 font-medium mb-4">
                  <Zap className="h-4 w-4" /> Extraction Model
                </h3>
                <FormField
                  control={form.control}
                  name="extractionModel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-300">
                        Text Extraction
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-900 border-slate-700 text-slate-200">
                            <SelectValue placeholder="Select extraction model" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-900 border-slate-700 text-slate-200">
                          {availableModels.map((model) => (
                            <SelectItem
                              key={`extraction-${model.id}`}
                              value={model.id}
                            >
                              {model.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription className="text-slate-400">
                        Fast model for document processing
                      </FormDescription>
                      <FormMessage className="text-red-400" />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <h3 className="flex items-center gap-2 text-purple-400 font-medium mb-4">
                  <Brain className="h-4 w-4" /> Generation Model
                </h3>
                <FormField
                  control={form.control}
                  name="generationModel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-300">
                        Content Generation
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-900 border-slate-700 text-slate-200">
                            <SelectValue placeholder="Select generation model" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-900 border-slate-700 text-slate-200">
                          {availableModels.map((model) => (
                            <SelectItem
                              key={`generation-${model.id}`}
                              value={model.id}
                            >
                              {model.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription className="text-slate-400">
                        High-quality model for flashcard creation
                      </FormDescription>
                      <FormMessage className="text-red-400" />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="baseUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-300 font-medium">
                      API Base URL
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://api.example.com"
                        {...field}
                        className="!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500"
                      />
                    </FormControl>
                    <FormMessage className="text-red-400" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="apiKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-300 font-medium">
                      API Key
                    </FormLabel>
                    <div className="relative">
                      <FormControl>
                        <Input
                          type={showApiKey ? "text" : "password"}
                          placeholder="Enter your API key"
                          {...field}
                          className="!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500"
                        />
                      </FormControl>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-400 hover:text-purple-300"
                        onClick={togglePasswordVisibility}
                      >
                        {showApiKey ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <FormDescription className="text-slate-400">
                      Your API key is used securely and never stored
                      permanently.
                    </FormDescription>
                    <FormMessage className="text-red-400" />
                  </FormItem>
                )}
              />
            </div>

            <div className="bg-slate-700 p-4 rounded-lg border border-slate-600">
              <div className="flex items-start gap-3">
                <Shield className="h-5 w-5 text-purple-400 mt-0.5" />
                <div>
                  <p className="text-slate-200 font-medium">
                    Security Information
                  </p>
                  <p className="text-slate-300 text-sm mt-1">
                    Your API key is sent securely to our backend and used only
                    for your current session. We don't store your credentials
                    permanently. All AI requests are processed through your
                    configured provider using your own API key, so you maintain
                    control of usage and costs.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate("/")}
                disabled={isSubmitting}
                className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300 disabled:opacity-50"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  "Save Configuration"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default AIConfigurationSection;
