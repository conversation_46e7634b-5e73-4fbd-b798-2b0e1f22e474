import { createClient } from "@supabase/supabase-js";
import { Database } from "../types/supabase"; // Assuming you will generate this type

// Use environment variables - fallback values only for development
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// In production, these should always be set via environment variables
if (!supabaseUrl) {
  throw new Error("VITE_SUPABASE_URL environment variable is required");
}

if (!supabaseAnonKey) {
  throw new Error("VITE_SUPABASE_ANON_KEY environment variable is required");
}

console.log("✓ Supabase client initialized");
console.log("✓ URL:", supabaseUrl);
console.log("✓ Using anon key:", supabaseAnonKey ? "Configured" : "Missing");

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);
