import { AIProviderSettings } from "@/types";

/**
 * Set AI provider settings in localStorage
 */
export function setAIProviderSettings(settings: AIProviderSettings): void {
  // Ensure model is set for backward compatibility
  const updatedSettings = {
    ...settings,
    model: settings.generationModel, // Keep model field aligned with generationModel
  };
  localStorage.setItem("aiProviderSettings", JSON.stringify(updatedSettings));
}

/**
 * Get AI provider settings from localStorage
 */
export function getAIProviderSettings(): AIProviderSettings {
  const systemDefaultProvider = "OpenRouter";
  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);
  const systemDefaultModels = getAvailableModels(systemDefaultProvider);

  const defaultExtractionModelId = "google/gemini-flash-1.5";
  const defaultGenerationModelId = "google/gemini-pro-1.5";

  const systemDefaultExtractionModel =
    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||
    systemDefaultModels[0]?.id ||
    "";
  const systemDefaultGenerationModel =
    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||
    systemDefaultModels[0]?.id ||
    "";

  const systemDefaults: AIProviderSettings = {
    provider: systemDefaultProvider,
    baseUrl: systemDefaultBaseUrl,
    apiKey: "", // Never use environment variables for API keys on client-side
    extractionModel: systemDefaultExtractionModel,
    generationModel: systemDefaultGenerationModel,
    model: systemDefaultGenerationModel, // Legacy field, matches generationModel
  };

  const storedSettings = localStorage.getItem("aiProviderSettings");
  if (!storedSettings) {
    return systemDefaults;
  }

  try {
    const parsedSettings = JSON.parse(
      storedSettings
    ) as Partial<AIProviderSettings>;

    const currentProvider = parsedSettings.provider || systemDefaults.provider;
    const modelsForCurrentProvider = getAvailableModels(currentProvider);

    let finalExtractionModel = parsedSettings.extractionModel;
    if (
      !finalExtractionModel ||
      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)
    ) {
      finalExtractionModel =
        modelsForCurrentProvider.find(
          (m) => m.id === systemDefaults.extractionModel
        )?.id ||
        modelsForCurrentProvider[0]?.id ||
        "";
    }

    let finalGenerationModel = parsedSettings.generationModel;
    if (
      !finalGenerationModel ||
      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)
    ) {
      finalGenerationModel =
        modelsForCurrentProvider.find(
          (m) => m.id === systemDefaults.generationModel
        )?.id ||
        modelsForCurrentProvider[0]?.id ||
        "";
    }

    return {
      provider: currentProvider,
      baseUrl:
        parsedSettings.baseUrl ||
        getDefaultBaseUrl(currentProvider) ||
        systemDefaults.baseUrl,
      apiKey: parsedSettings.apiKey || systemDefaults.apiKey,
      extractionModel: finalExtractionModel,
      generationModel: finalGenerationModel,
      model: finalGenerationModel, // Legacy field always matches generationModel
    };
  } catch (e) {
    // console.error("Failed to parse AI provider settings, returning system defaults:", e);
    return systemDefaults;
  }
}

/**
 * Check if AI provider settings are configured
 */
export function isAIProviderConfigured(): boolean {
  const settings = getAIProviderSettings();
  return Boolean(settings.apiKey.trim());
}

/**
 * Get list of available AI models by provider
 */
export function getAvailableModels(
  provider: string
): { id: string; name: string }[] {
  switch (provider) {
    case "OpenRouter":
      return [
        { id: "google/gemini-flash-1.5", name: "Google Gemini Flash 1.5" },
        {
          id: "google/gemini-pro-1.5",
          name: "Google Gemini Pro 1.5 (Recommended)",
        },
      ];
    default:
      return [];
  }
}

/**
 * Get available AI providers
 */
export function getAvailableProviders(): string[] {
  return ["OpenRouter"];
}

/**
 * Get default base URL for a provider
 */
export function getDefaultBaseUrl(provider: string): string {
  switch (provider) {
    case "OpenRouter":
      return "https://openrouter.ai/api/v1";
    default:
      return "";
  }
}

/**
 * Get recommended model for specific tasks
 * This follows the PRD recommendations
 */
export function getRecommendedModelForTask(
  task: "extraction" | "generation"
): string {
  const settings = getAIProviderSettings();
  switch (task) {
    case "extraction":
      return settings.extractionModel || "google/gemini-flash-1.5";
    case "generation":
      return settings.generationModel || "google/gemini-pro-1.5";
    default:
      return settings.generationModel || "google/gemini-pro-1.5";
  }
}
