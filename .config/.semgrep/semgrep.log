2025-06-02 17:44:30,230 - semgrep.run_scan - DEBUG - semgrep version 1.2.0
2025-06-02 17:44:30,240 - semgrep.config_resolver - DEBUG - Loading local config from /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json
2025-06-02 17:44:30,248 - semgrep.config_resolver - DEBUG - Done loading local config from /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json
2025-06-02 17:44:30,252 - semgrep.config_resolver - DEBUG - Saving rules to /tmp/semgrep-fur6tj9o.rules
2025-06-02 17:44:30,418 - semgrep.semgrep_core - DEBUG - Failed to open resource semgrep-core-proprietary: [Errno 2] No such file or directory: '/tmp/_MEIC0TmjD/semgrep/bin/semgrep-core-proprietary'.
2025-06-02 17:44:30,889 - semgrep.rule_lang - DEBUG - semgrep-core validation response: valid=True
2025-06-02 17:44:30,890 - semgrep.rule_lang - DEBUG - semgrep-core validation succeeded
2025-06-02 17:44:30,890 - semgrep.rule_lang - DEBUG - RPC validation succeeded
2025-06-02 17:44:30,890 - semgrep.config_resolver - DEBUG - loaded 1 configs in 0.6502218246459961
2025-06-02 17:44:30,957 - semgrep.run_scan - VERBOSE - running 711 rules from 1 config /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json_0
2025-06-02 17:44:30,958 - semgrep.run_scan - VERBOSE - No .semgrepignore found. Using default .semgrepignore rules. See the docs for the list of default ignores: https://semgrep.dev/docs/cli-usage/#ignore-files
2025-06-02 17:44:30,959 - semgrep.run_scan - VERBOSE - Rules:
2025-06-02 17:44:30,959 - semgrep.run_scan - VERBOSE - <SKIPPED DATA (too many entries; use --max-log-list-entries)>
2025-06-02 17:44:31,818 - semgrep.core_runner - DEBUG - Passing whole rules directly to semgrep_core
2025-06-02 17:44:32,068 - semgrep.core_runner - DEBUG - Running Semgrep engine with command:
2025-06-02 17:44:32,068 - semgrep.core_runner - DEBUG - /tmp/_MEIC0TmjD/semgrep/bin/opengrep-core -json -rules /tmp/tmpwwaobo0d.json -j 8 -targets /tmp/tmpagwenuyk -timeout 5 -timeout_threshold 3 -max_memory 0 -fast
2025-06-02 17:44:54,561 - semgrep.core_runner - DEBUG - --- semgrep-core stderr ---
[00.07][[34mINFO[0m]: Executed as: /tmp/_MEIC0TmjD/semgrep/bin/opengrep-core -json -rules /tmp/tmpwwaobo0d.json -j 8 -targets /tmp/tmpagwenuyk -timeout 5 -timeout_threshold 3 -max_memory 0 -fast
[00.07][[34mINFO[0m]: Version: 1.2.0
[00.07][[34mINFO[0m]: Parsing rules in /tmp/tmpwwaobo0d.json
[00.51][[34mINFO[0m]: scan: processing 589 files (skipping 0), with 454 rules (skipping 0 )
[01.16][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes/flashcardRoutes.ts func: ???]
[0m[01.29][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes/flashcardRoutes.ts func: ???]
[0m[03.64][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes/flashcards.ts func: ???]
[0m[04.02][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes/flashcards.ts func: ???]
[0m[04.17][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes/flashcards.ts func: ???]
[0m[04.29][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes/flashcards.ts func: ???]
[0m[04.38][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: server/routes/flashcards.ts func: ???]
[0m[06.02][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/flashcards/FlashcardForm.tsx func: ???]
[0m[06.08][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/InlineDocumentViewer.tsx func: ???]
[0m[06.11][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/flashcards/FlashcardForm.tsx func: ???]
[0m[06.19][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/InlineDocumentViewer.tsx func: ???]
[0m[06.71][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/components/dashboard/StudySection.tsx func: ???]
[0m[06.78][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/components/dashboard/StudySection.tsx func: ???]
[0m[07.15][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function file: client/src/components/document/InlineDocumentViewer.tsx func: ???]
[0m[07.88][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/dashboard/UploadSection.tsx func: ???]
[0m[08.09][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.path-traversal.path-join-resolve-traversal file: server/routes/quizRoutes.ts func: generateQuestionsFromAI:64385]
[0m[08.18][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.path-traversal.path-join-resolve-traversal file: server/routes/quizRoutes.ts func: ???]
[0m[08.55][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: server/routes/quizRoutes.ts func: generateQuestionsFromAI:64385]
[0m[08.56][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/QuestionForm.tsx func: ???]
[0m[08.64][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/QuestionForm.tsx func: ???]
[0m[08.65][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: server/routes/quizRoutes.ts func: ???]
[0m[08.79][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: server/routes/quizRoutes.ts func: generateQuestionsFromAI:64385]
[0m[08.90][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: server/routes/quizRoutes.ts func: ???]
[0m[08.92][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/quiz/QuizPlayer.tsx func: ???]
[0m[09.12][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/QuizPlayer.tsx func: ???]
[0m[09.41][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/QuizPlayer.tsx func: ???]
[0m[10.57][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: client/src/components/flashcards/FlashcardManager.tsx func: ???]
[0m[10.80][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: server/routes/quizRoutes.ts func: generateQuestionsFromAI:64385]
[0m[10.94][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: server/routes/quizRoutes.ts func: ???]
[0m[11.22][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/pages/DashboardPage.tsx func: ???]
[0m[11.43][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/EnhancedDocumentUpload.tsx func: ???]
[0m[11.49][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes/quizExpressRoutes.ts func: ???]
[0m[11.51][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/EnhancedDocumentUpload.tsx func: ???]
[0m[11.60][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/quiz/SRSQuizMode.tsx func: ???]
[0m[11.69][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/quiz/SRSQuizMode.tsx func: ???]
[0m[11.82][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes/quizExpressRoutes.ts func: ???]
[0m[11.84][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/SRSQuizMode.tsx func: ???]
[0m[11.93][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/SRSQuizMode.tsx func: ???]
[0m[11.93][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes/quizExpressRoutes.ts func: ???]
[0m[12.15][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes/quizExpressRoutes.ts func: ???]
[0m[12.26][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: server/routes/quizExpressRoutes.ts func: ???]
[0m[12.42][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: client/src/components/ai/AIConfigurationSection.tsx func: ???]
[0m[12.62][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/AiQuestionGenerator.tsx func: ???]
[0m[12.70][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/AiQuestionGenerator.tsx func: ???]
[0m[12.83][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/AiQuestionGenerator.tsx func: ???]
[0m[12.97][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/AiQuestionGenerator.tsx func: ???]
[0m[14.28][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: server/routes/documentRoutes.ts func: ???]
[0m[14.48][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: server/routes/documentRoutes.ts func: ???]
[0m[14.84][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[14.99][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/flashcards/AiFlashcardGenerator.tsx func: ???]
[0m[15.17][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/flashcards/AiFlashcardGenerator.tsx func: ???]
[0m[15.19][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[15.28][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes/documentRoutes.ts func: ???]
[0m[15.31][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/flashcards/AiFlashcardGenerator.tsx func: ???]
[0m[15.36][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes/documentRoutes.ts func: ???]
[0m[15.47][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[15.63][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes/documentRoutes.ts func: ???]
[0m[15.69][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[15.79][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes/documentRoutes.ts func: ???]
[0m[16.08][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[16.09][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: server/routes/aiRoutes.ts func: ???]
[0m[16.23][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: server/routes/aiRoutes.ts func: ???]
[0m[16.24][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.path-traversal.path-join-resolve-traversal file: supabase/functions/generate-quiz-questions/index.ts func: ???]
[0m[16.42][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[16.45][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write file: server/routes/documentRoutes.ts func: ???]
[0m[16.47][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: supabase/functions/generate-quiz-questions/index.ts func: ???]
[0m[16.49][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.tainted-sql-string file: server/routes/aiRoutes.ts func: ???]
[0m[16.50][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[16.58][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write file: server/routes/documentRoutes.ts func: ???]
[0m[16.64][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: supabase/functions/generate-quiz-questions/index.ts func: ???]
[0m[16.75][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes/aiRoutes.ts func: ???]
[0m[16.83][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write file: server/routes/documentRoutes.ts func: ???]
[0m[17.08][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write file: server/routes/documentRoutes.ts func: ???]
[0m[17.41][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.express-ssrf file: server/routes/aiRoutes.ts func: ???]
[0m[17.44][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.tainted-sql-string file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[17.59][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes/aiRoutes.ts func: ???]
[0m[17.67][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.tainted-sql-string file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[17.89][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes/aiRoutes.ts func: ???]
[0m[18.03][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes/aiRoutes.ts func: ???]
[0m[18.13][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes/documentRoutes.ts func: ???]
[0m[18.22][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: server/routes/aiRoutes.ts func: ???]
[0m[18.30][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes/documentRoutes.ts func: ???]
[0m[18.55][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes/documentRoutes.ts func: ???]
[0m[18.70][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes/documentRoutes.ts func: ???]
[0m[19.14][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes/documentRoutes.ts func: ???]
[0m[19.23][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes/documentRoutes.ts func: ???]
[0m[19.36][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes/documentRoutes.ts func: ???]
[0m[19.75][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes/documentRoutes.ts func: ???]
[0m[19.88][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes/documentRoutes.ts func: ???]
[0m[20.58][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: server/routes/documentRoutes.ts func: ???]
[0m[20.86][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: server/routes/documentRoutes.ts func: ???]
[0m[22.41][[34mINFO[0m]: Custom ignore pattern: None
[22.41][[34mINFO[0m]: Custom ignore pattern: None
--- end semgrep-core stderr ---
2025-06-02 17:44:54,692 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = dba75b85fc567707c179285ad63f98b7063f81ea873c69c1923f927f9a40e33d2ebca9d8fb2a4db3d5fc23dc43b616b68ac8d0a549f0b9ca1a8738022e8b7438_0
2025-06-02 17:44:54,693 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 90e85da291820bc77ea34f456a34bf3d1ac263ba77b46922c1d8937df89cab5d4f13c14b64fb6bd1b1bd5ce168892967385395d06e530bcb5a3b914077b30099_0
2025-06-02 17:44:54,694 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 90e85da291820bc77ea34f456a34bf3d1ac263ba77b46922c1d8937df89cab5d4f13c14b64fb6bd1b1bd5ce168892967385395d06e530bcb5a3b914077b30099_0
2025-06-02 17:44:54,694 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 90e85da291820bc77ea34f456a34bf3d1ac263ba77b46922c1d8937df89cab5d4f13c14b64fb6bd1b1bd5ce168892967385395d06e530bcb5a3b914077b30099_0
2025-06-02 17:44:54,695 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 358dbc04c40070c48e491d4cdfff2a60e49e9cd7ad59202b0fe88ac4c11c7e93dac50436800cb723701dac9f6c73c6aadbba5d1458ffc58df665830b0af90d37_0
2025-06-02 17:44:54,696 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 65d8818d9695321d8ee91046e0edf740176620a1e436c6e0664191c4d1a735c9db70df363c8f000098ef6aece04cc7745c56754cd4a4d44b751d5de2ec78ef43_0
2025-06-02 17:44:54,696 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 65d8818d9695321d8ee91046e0edf740176620a1e436c6e0664191c4d1a735c9db70df363c8f000098ef6aece04cc7745c56754cd4a4d44b751d5de2ec78ef43_0
2025-06-02 17:44:54,697 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 65d8818d9695321d8ee91046e0edf740176620a1e436c6e0664191c4d1a735c9db70df363c8f000098ef6aece04cc7745c56754cd4a4d44b751d5de2ec78ef43_0
2025-06-02 17:44:54,698 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.generic-api-key') match_id = 78c6568e306f8a3a7c9517950b7dc28fd1010f213eaa6963912ae5dff4ffe490fff547ea81d13183c737745afa87eacb08f5946b6b3bcf33fac5d2067f617ab8_0
2025-06-02 17:44:54,699 - semgrep.rule_match - DEBUG - match_key = ('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 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 (?!(?i).*(client|endpoint|vpn|_ec2_|aws_|authorize|author|define|config|credential|setting|sample|xxxxxx|000000|buffer|delete|aaaaaa|fewfwef|getenv|env_|system|example|ecdsa|sha256|sha1|sha2|md5|alert|wizard|target|onboard|welcome|page|exploit|experiment|expire|rabbitmq|scraper|widget|music|dns_|dns-|yahoo|want|json|action|script|fix_|fix-|develop|compas|stripe|service|master|metric|tech|gitignore|rich|open|stack|irc_|irc-|sublime|kohana|has_|has-|fabric|wordpres|role|osx_|osx-|boost|addres|queue|working|sandbox|internet|print|vision|tracking|being|generator|traffic|world|pull|rust|watcher|small|auth|full|hash|more|install|auto|complete|learn|paper|installer|research|acces|last|binding|spine|into|chat|algorithm|resource|uploader|video|maker|next|proc|lock|robot|snake|patch|matrix|drill|terminal|term|stuff|genetic|generic|identity|audit|pattern|audio|web_|web-|crud|problem|statu|cms-|cms_|arch|coffee|workflow|changelog|another|uiview|content|kitchen|gnu_|gnu-|gnu\\.|conf|couchdb|client|opencv|rendering|update|concept|varnish|gui_|gui-|gui\\.|version|shared|extra|product|still|not_|not-|not\\.|drop|ring|png_|png-|png\\.|actively|import|output|backup|start|embedded|registry|pool|semantic|instagram|bash|system|ninja|drupal|jquery|polyfill|physic|league|guide|pack|synopsi|sketch|injection|svg_|svg-|svg\\.|friendly|wave|convert|manage|camera|link|slide|timer|wrapper|gallery|url_|url-|url\\.|todomvc|requirej|party|http|payment|async|library|home|coco|gaia|display|universal|func|metadata|hipchat|under|room|config|personal|realtime|resume|database|testing|tiny|basic|forum|meetup|yet_|yet-|yet\\.|cento|dead|fluentd|editor|utilitie|run_|run-|run\\.|box_|box-|box\\.|bot_|bot-|bot\\.|making|sample|group|monitor|ajax|parallel|cassandra|ultimate|site|get_|get-|get\\.|gen_|gen-|gen\\.|gem_|gem-|gem\\.|extended|image|knife|asset|nested|zero|plugin|bracket|mule|mozilla|number|act_|act-|act\\.|map_|map-|map\\.|micro|debug|openshift|chart|expres|backend|task|source|translate|jbos|composer|sqlite|profile|mustache|mqtt|yeoman|have|builder|smart|like|oauth|school|guideline|captcha|filter|bitcoin|bridge|color|toolbox|discovery|new_|new-|new\\.|dashboard|when|setting|level|post|standard|port|platform|yui_|yui-|yui\\.|grunt|animation|haskell|icon|latex|cheat|lua_|lua-|lua\\.|gulp|case|author|without|simulator|wifi|directory|lisp|list|flat|adventure|story|storm|gpu_|gpu-|gpu\\.|store|caching|attention|solr|logger|demo|shortener|hadoop|finder|phone|pipeline|range|textmate|showcase|app_|app-|app\\.|idiomatic|edit|our_|our-|our\\.|out_|out-|out\\.|sentiment|linked|why_|why-|why\\.|local|cube|gmail|job_|job-|job\\.|rpc_|rpc-|rpc\\.|contest|tcp_|tcp-|tcp\\.|usage|buildout|weather|transfer|automated|sphinx|issue|sas_|sas-|sas\\.|parallax|jasmine|addon|machine|solution|dsl_|dsl-|dsl\\.|episode|menu|theme|best|adapter|debugger|chrome|tutorial|life|step|people|joomla|paypal|developer|solver|team|current|love|visual|date|data|canva|container|future|xml_|xml-|xml\\.|twig|nagio|spatial|original|sync|archived|refinery|science|mapping|gitlab|play|ext_|ext-|ext\\.|session|impact|set_|set-|set\\.|see_|see-|see\\.|migration|commit|community|shopify|what\'|cucumber|statamic|mysql|location|tower|line|code|amqp|hello|send|index|high|notebook|alloy|python|field|document|soap|edition|email|php_|php-|php\\.|command|transport|official|upload|study|secure|angularj|akka|scalable|package|request|con_|con-|con\\.|flexible|security|comment|module|flask|graph|flash|apache|change|window|space|lambda|sheet|bookmark|carousel|friend|objective|jekyll|bootstrap|first|article|gwt_|gwt-|gwt\\.|classic|media|websocket|touch|desktop|real|read|recorder|moved|storage|validator|add-on|pusher|scs_|scs-|scs\\.|inline|asp_|asp-|asp\\.|timeline|base|encoding|ffmpeg|kindle|tinymce|pretty|jpa_|jpa-|jpa\\.|used|user|required|webhook|download|resque|espresso|cloud|mongo|benchmark|pure|cakephp|modx|mode|reactive|fuel|written|flickr|mail|brunch|meteor|dynamic|neo_|neo-|neo\\.|new_|new-|new\\.|net_|net-|net\\.|typo|type|keyboard|erlang|adobe|logging|ckeditor|message|iso_|iso-|iso\\.|hook|ldap|folder|reference|railscast|www_|www-|www\\.|tracker|azure|fork|form|digital|exporter|skin|string|template|designer|gollum|fluent|entity|language|alfred|summary|wiki|kernel|calendar|plupload|symfony|foundry|remote|talk|search|dev_|dev-|dev\\.|del_|del-|del\\.|token|idea|sencha|selector|interface|create|fun_|fun-|fun\\.|groovy|query|grail|red_|red-|red\\.|laravel|monkey|slack|supported|instant|value|center|latest|work|but_|but-|but\\.|bug_|bug-|bug\\.|virtual|tweet|statsd|studio|path|real-time|frontend|notifier|coding|tool|firmware|flow|random|mediawiki|bosh|been|beer|lightbox|theory|origin|redmine|hub_|hub-|hub\\.|require|pro_|pro-|pro\\.|ant_|ant-|ant\\.|any_|any-|any\\.|recipe|closure|mapper|event|todo|model|redi|provider|rvm_|rvm-|rvm\\.|program|memcached|rail|silex|foreman|activity|license|strategy|batch|streaming|fast|use_|use-|use\\.|usb_|usb-|usb\\.|impres|academy|slider|please|layer|cros|now_|now-|now\\.|miner|extension|own_|own-|own\\.|app_|app-|app\\.|debian|symphony|example|feature|serie|tree|project|runner|entry|leetcode|layout|webrtc|logic|login|worker|toolkit|mocha|support|back|inside|device|jenkin|contact|fake|awesome|ocaml|bit_|bit-|bit\\.|drive|screen|prototype|gist|binary|nosql|rest|overview|dart|dark|emac|mongoid|solarized|homepage|emulator|commander|django|yandex|gradle|xcode|writer|crm_|crm-|crm\\.|jade|startup|error|using|format|name|spring|parser|scratch|magic|try_|try-|try\\.|rack|directive|challenge|slim|counter|element|chosen|doc_|doc-|doc\\.|meta|should|button|packet|stream|hardware|android|infinite|password|software|ghost|xamarin|spec|chef|interview|hubot|mvc_|mvc-|mvc\\.|exercise|leaflet|launcher|air_|air-|air\\.|photo|board|boxen|way_|way-|way\\.|computing|welcome|notepad|portfolio|cat_|cat-|cat\\.|can_|can-|can\\.|magento|yaml|domain|card|yii_|yii-|yii\\.|checker|browser|upgrade|only|progres|aura|ruby_|ruby-|ruby\\.|polymer|util|lite|hackathon|rule|log_|log-|log\\.|opengl|stanford|skeleton|history|inspector|help|soon|selenium|lab_|lab-|lab\\.|scheme|schema|look|ready|leveldb|docker|game|minimal|logstash|messaging|within|heroku|mongodb|kata|suite|picker|win_|win-|win\\.|wip_|wip-|wip\\.|panel|started|starter|front-end|detector|deploy|editing|based|admin|capture|spree|page|bundle|goal|rpg_|rpg-|rpg\\.|setup|side|mean|reader|cookbook|mini|modern|seed|dom_|dom-|dom\\.|doc_|doc-|doc\\.|dot_|dot-|dot\\.|syntax|sugar|loader|website|make|kit_|kit-|kit\\.|protocol|human|daemon|golang|manager|countdown|connector|swagger|map_|map-|map\\.|mac_|mac-|mac\\.|man_|man-|man\\.|orm_|orm-|orm\\.|org_|org-|org\\.|little|zsh_|zsh-|zsh\\.|shop|show|workshop|money|grid|server|octopres|svn_|svn-|svn\\.|ember|embed|general|file|important|dropbox|portable|public|docpad|fish|sbt_|sbt-|sbt\\.|done|para|network|common|readme|popup|simple|purpose|mirror|single|cordova|exchange|object|design|gateway|account|lamp|intellij|math|mit_|mit-|mit\\.|control|enhanced|emitter|multi|add_|add-|add\\.|about|socket|preview|vagrant|cli_|cli-|cli\\.|powerful|top_|top-|top\\.|radio|watch|fluid|amazon|report|couchbase|automatic|detection|sprite|pyramid|portal|advanced|plu_|plu-|plu\\.|runtime|git_|git-|git\\.|uri_|uri-|uri\\.|haml|node|sql_|sql-|sql\\.|cool|core|obsolete|handler|iphone|extractor|array|copy|nlp_|nlp-|nlp\\.|reveal|pop_|pop-|pop\\.|engine|parse|check|html|nest|all_|all-|all\\.|chinese|buildpack|what|tag_|tag-|tag\\.|proxy|style|cookie|feed|restful|compiler|creating|prelude|context|java|rspec|mock|backbone|light|spotify|flex|related|shell|which|clas|webapp|swift|ansible|unity|console|tumblr|export|campfire|conway\'|made|riak|hero|here|unix|unit|glas|smtp|how_|how-|how\\.|hot_|hot-|hot\\.|debug|release|diff|player|easy|right|old_|old-|old\\.|animate|time|push|explorer|course|training|nette|router|draft|structure|note|salt|where|spark|trello|power|method|social|via_|via-|via\\.|vim_|vim-|vim\\.|select|webkit|github|ftp_|ftp-|ftp\\.|creator|mongoose|led_|led-|led\\.|movie|currently|pdf_|pdf-|pdf\\.|load|markdown|phalcon|input|custom|atom|oracle|phonegap|ubuntu|great|rdf_|rdf-|rdf\\.|popcorn|firefox|zip_|zip-|zip\\.|cuda|dotfile|static|openwrt|viewer|powered|graphic|les_|les-|les\\.|doe_|doe-|doe\\.|maven|word|eclipse|lab_|lab-|lab\\.|hacking|steam|analytic|option|abstract|archive|reality|switcher|club|write|kafka|arduino|angular|online|title|don\'t|contao|notice|analyzer|learning|zend|external|staging|busines|tdd_|tdd-|tdd\\.|scanner|building|snippet|modular|bower|stm_|stm-|stm\\.|lib_|lib-|lib\\.|alpha|mobile|clean|linux|nginx|manifest|some|raspberry|gnome|ide_|ide-|ide\\.|block|statistic|info|drag|youtube|koan|facebook|paperclip|art_|art-|art\\.|quality|tab_|tab-|tab\\.|need|dojo|shield|computer|stat|state|twitter|utility|converter|hosting|devise|liferay|updated|force|tip_|tip-|tip\\.|behavior|active|call|answer|deck|better|principle|ches|bar_|bar-|bar\\.|reddit|three|haxe|just|plug-in|agile|manual|tetri|super|beta|parsing|doctrine|minecraft|useful|perl|sharing|agent|switch|view|dash|channel|repo|pebble|profiler|warning|cluster|running|markup|evented|mod_|mod-|mod\\.|share|csv_|csv-|csv\\.|response|good|house|connect|built|build|find|ipython|webgl|big_|big-|big\\.|google|scala|sdl_|sdl-|sdl\\.|sdk_|sdk-|sdk\\.|native|day_|day-|day\\.|puppet|text|routing|helper|linkedin|crawler|host|guard|merchant|poker|over|writing|free|classe|component|craft|nodej|phoenix|longer|quick|lazy|memory|clone|hacker|middleman|factory|motion|multiple|tornado|hack|ssh_|ssh-|ssh\\.|review|vimrc|driver|driven|blog|particle|table|intro|importer|thrift|xmpp|framework|refresh|react|font|librarie|variou|formatter|analysi|karma|scroll|tut_|tut-|tut\\.|apple|tag_|tag-|tag\\.|tab_|tab-|tab\\.|category|ionic|cache|homebrew|reverse|english|getting|shipping|clojure|boot|book|branch|combination|combo)) 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 (?!(^0x0*|^pub)|.*\\.(bin|json|exe)$|.*(?i)(Client|Factory)$|(^__[A-Za-z]+__$)|^(12345|abcd)|^\\d+(\\.\\d+)?$) 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 entropy (?i)(?:key|api|token|secret|client|passwd|password|auth|access)(?:[0-9a-z\\-_\\t.]{0,20})(?:[\\s|\']|[\\s|"]){0,3}(?:=|>|:=|\\|\\|:|<=|=>|:)(?:\'|@\\"|\\"|\\s|=|\\x60){0,5}(?!([a-z]+\\.[a-zA-Z]+)|.*(\\d{4}-\\d{2}-\\d{2})|:*(?!("|\'))[0-9A-Za-z]+\\.[0-9A-Za-z]+,|[A-Z]+_[A-Z]+_)(?P<CONTENT>[0-9a-z\\-_.=\\~@]{10,150})(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$) (\\d\\.\\d\\.\\d-}|([\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3})|(\\w)\\1{5}|(?i)keywords|xxxx|eeeeeeee|0000|\\*\\*\\*|example|test|author=|author("|\')|preview|[A-Z]+_KEY|[.]value|[.]key|-\\d\\.\\d\\.) (\\w|\\.)\\1{5} .*((?i)omitted|arn:aws|(?i)(pub.*key|public.*key)|(?i)clientToken|symbol|cache|author\\.).*', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.generic-api-key') match_id = e568a8303898f65c43738ce9b467b3811ade960877866255d500f56f4cf5486be06aa8df9e775fee32241e3dbf0b475534fcd8256ff0310b842e905efeecb8ee_0
2025-06-02 17:44:54,701 - semgrep.rule_match - DEBUG - match_key = ('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 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 (?!(?i).*(client|endpoint|vpn|_ec2_|aws_|authorize|author|define|config|credential|setting|sample|xxxxxx|000000|buffer|delete|aaaaaa|fewfwef|getenv|env_|system|example|ecdsa|sha256|sha1|sha2|md5|alert|wizard|target|onboard|welcome|page|exploit|experiment|expire|rabbitmq|scraper|widget|music|dns_|dns-|yahoo|want|json|action|script|fix_|fix-|develop|compas|stripe|service|master|metric|tech|gitignore|rich|open|stack|irc_|irc-|sublime|kohana|has_|has-|fabric|wordpres|role|osx_|osx-|boost|addres|queue|working|sandbox|internet|print|vision|tracking|being|generator|traffic|world|pull|rust|watcher|small|auth|full|hash|more|install|auto|complete|learn|paper|installer|research|acces|last|binding|spine|into|chat|algorithm|resource|uploader|video|maker|next|proc|lock|robot|snake|patch|matrix|drill|terminal|term|stuff|genetic|generic|identity|audit|pattern|audio|web_|web-|crud|problem|statu|cms-|cms_|arch|coffee|workflow|changelog|another|uiview|content|kitchen|gnu_|gnu-|gnu\\.|conf|couchdb|client|opencv|rendering|update|concept|varnish|gui_|gui-|gui\\.|version|shared|extra|product|still|not_|not-|not\\.|drop|ring|png_|png-|png\\.|actively|import|output|backup|start|embedded|registry|pool|semantic|instagram|bash|system|ninja|drupal|jquery|polyfill|physic|league|guide|pack|synopsi|sketch|injection|svg_|svg-|svg\\.|friendly|wave|convert|manage|camera|link|slide|timer|wrapper|gallery|url_|url-|url\\.|todomvc|requirej|party|http|payment|async|library|home|coco|gaia|display|universal|func|metadata|hipchat|under|room|config|personal|realtime|resume|database|testing|tiny|basic|forum|meetup|yet_|yet-|yet\\.|cento|dead|fluentd|editor|utilitie|run_|run-|run\\.|box_|box-|box\\.|bot_|bot-|bot\\.|making|sample|group|monitor|ajax|parallel|cassandra|ultimate|site|get_|get-|get\\.|gen_|gen-|gen\\.|gem_|gem-|gem\\.|extended|image|knife|asset|nested|zero|plugin|bracket|mule|mozilla|number|act_|act-|act\\.|map_|map-|map\\.|micro|debug|openshift|chart|expres|backend|task|source|translate|jbos|composer|sqlite|profile|mustache|mqtt|yeoman|have|builder|smart|like|oauth|school|guideline|captcha|filter|bitcoin|bridge|color|toolbox|discovery|new_|new-|new\\.|dashboard|when|setting|level|post|standard|port|platform|yui_|yui-|yui\\.|grunt|animation|haskell|icon|latex|cheat|lua_|lua-|lua\\.|gulp|case|author|without|simulator|wifi|directory|lisp|list|flat|adventure|story|storm|gpu_|gpu-|gpu\\.|store|caching|attention|solr|logger|demo|shortener|hadoop|finder|phone|pipeline|range|textmate|showcase|app_|app-|app\\.|idiomatic|edit|our_|our-|our\\.|out_|out-|out\\.|sentiment|linked|why_|why-|why\\.|local|cube|gmail|job_|job-|job\\.|rpc_|rpc-|rpc\\.|contest|tcp_|tcp-|tcp\\.|usage|buildout|weather|transfer|automated|sphinx|issue|sas_|sas-|sas\\.|parallax|jasmine|addon|machine|solution|dsl_|dsl-|dsl\\.|episode|menu|theme|best|adapter|debugger|chrome|tutorial|life|step|people|joomla|paypal|developer|solver|team|current|love|visual|date|data|canva|container|future|xml_|xml-|xml\\.|twig|nagio|spatial|original|sync|archived|refinery|science|mapping|gitlab|play|ext_|ext-|ext\\.|session|impact|set_|set-|set\\.|see_|see-|see\\.|migration|commit|community|shopify|what\'|cucumber|statamic|mysql|location|tower|line|code|amqp|hello|send|index|high|notebook|alloy|python|field|document|soap|edition|email|php_|php-|php\\.|command|transport|official|upload|study|secure|angularj|akka|scalable|package|request|con_|con-|con\\.|flexible|security|comment|module|flask|graph|flash|apache|change|window|space|lambda|sheet|bookmark|carousel|friend|objective|jekyll|bootstrap|first|article|gwt_|gwt-|gwt\\.|classic|media|websocket|touch|desktop|real|read|recorder|moved|storage|validator|add-on|pusher|scs_|scs-|scs\\.|inline|asp_|asp-|asp\\.|timeline|base|encoding|ffmpeg|kindle|tinymce|pretty|jpa_|jpa-|jpa\\.|used|user|required|webhook|download|resque|espresso|cloud|mongo|benchmark|pure|cakephp|modx|mode|reactive|fuel|written|flickr|mail|brunch|meteor|dynamic|neo_|neo-|neo\\.|new_|new-|new\\.|net_|net-|net\\.|typo|type|keyboard|erlang|adobe|logging|ckeditor|message|iso_|iso-|iso\\.|hook|ldap|folder|reference|railscast|www_|www-|www\\.|tracker|azure|fork|form|digital|exporter|skin|string|template|designer|gollum|fluent|entity|language|alfred|summary|wiki|kernel|calendar|plupload|symfony|foundry|remote|talk|search|dev_|dev-|dev\\.|del_|del-|del\\.|token|idea|sencha|selector|interface|create|fun_|fun-|fun\\.|groovy|query|grail|red_|red-|red\\.|laravel|monkey|slack|supported|instant|value|center|latest|work|but_|but-|but\\.|bug_|bug-|bug\\.|virtual|tweet|statsd|studio|path|real-time|frontend|notifier|coding|tool|firmware|flow|random|mediawiki|bosh|been|beer|lightbox|theory|origin|redmine|hub_|hub-|hub\\.|require|pro_|pro-|pro\\.|ant_|ant-|ant\\.|any_|any-|any\\.|recipe|closure|mapper|event|todo|model|redi|provider|rvm_|rvm-|rvm\\.|program|memcached|rail|silex|foreman|activity|license|strategy|batch|streaming|fast|use_|use-|use\\.|usb_|usb-|usb\\.|impres|academy|slider|please|layer|cros|now_|now-|now\\.|miner|extension|own_|own-|own\\.|app_|app-|app\\.|debian|symphony|example|feature|serie|tree|project|runner|entry|leetcode|layout|webrtc|logic|login|worker|toolkit|mocha|support|back|inside|device|jenkin|contact|fake|awesome|ocaml|bit_|bit-|bit\\.|drive|screen|prototype|gist|binary|nosql|rest|overview|dart|dark|emac|mongoid|solarized|homepage|emulator|commander|django|yandex|gradle|xcode|writer|crm_|crm-|crm\\.|jade|startup|error|using|format|name|spring|parser|scratch|magic|try_|try-|try\\.|rack|directive|challenge|slim|counter|element|chosen|doc_|doc-|doc\\.|meta|should|button|packet|stream|hardware|android|infinite|password|software|ghost|xamarin|spec|chef|interview|hubot|mvc_|mvc-|mvc\\.|exercise|leaflet|launcher|air_|air-|air\\.|photo|board|boxen|way_|way-|way\\.|computing|welcome|notepad|portfolio|cat_|cat-|cat\\.|can_|can-|can\\.|magento|yaml|domain|card|yii_|yii-|yii\\.|checker|browser|upgrade|only|progres|aura|ruby_|ruby-|ruby\\.|polymer|util|lite|hackathon|rule|log_|log-|log\\.|opengl|stanford|skeleton|history|inspector|help|soon|selenium|lab_|lab-|lab\\.|scheme|schema|look|ready|leveldb|docker|game|minimal|logstash|messaging|within|heroku|mongodb|kata|suite|picker|win_|win-|win\\.|wip_|wip-|wip\\.|panel|started|starter|front-end|detector|deploy|editing|based|admin|capture|spree|page|bundle|goal|rpg_|rpg-|rpg\\.|setup|side|mean|reader|cookbook|mini|modern|seed|dom_|dom-|dom\\.|doc_|doc-|doc\\.|dot_|dot-|dot\\.|syntax|sugar|loader|website|make|kit_|kit-|kit\\.|protocol|human|daemon|golang|manager|countdown|connector|swagger|map_|map-|map\\.|mac_|mac-|mac\\.|man_|man-|man\\.|orm_|orm-|orm\\.|org_|org-|org\\.|little|zsh_|zsh-|zsh\\.|shop|show|workshop|money|grid|server|octopres|svn_|svn-|svn\\.|ember|embed|general|file|important|dropbox|portable|public|docpad|fish|sbt_|sbt-|sbt\\.|done|para|network|common|readme|popup|simple|purpose|mirror|single|cordova|exchange|object|design|gateway|account|lamp|intellij|math|mit_|mit-|mit\\.|control|enhanced|emitter|multi|add_|add-|add\\.|about|socket|preview|vagrant|cli_|cli-|cli\\.|powerful|top_|top-|top\\.|radio|watch|fluid|amazon|report|couchbase|automatic|detection|sprite|pyramid|portal|advanced|plu_|plu-|plu\\.|runtime|git_|git-|git\\.|uri_|uri-|uri\\.|haml|node|sql_|sql-|sql\\.|cool|core|obsolete|handler|iphone|extractor|array|copy|nlp_|nlp-|nlp\\.|reveal|pop_|pop-|pop\\.|engine|parse|check|html|nest|all_|all-|all\\.|chinese|buildpack|what|tag_|tag-|tag\\.|proxy|style|cookie|feed|restful|compiler|creating|prelude|context|java|rspec|mock|backbone|light|spotify|flex|related|shell|which|clas|webapp|swift|ansible|unity|console|tumblr|export|campfire|conway\'|made|riak|hero|here|unix|unit|glas|smtp|how_|how-|how\\.|hot_|hot-|hot\\.|debug|release|diff|player|easy|right|old_|old-|old\\.|animate|time|push|explorer|course|training|nette|router|draft|structure|note|salt|where|spark|trello|power|method|social|via_|via-|via\\.|vim_|vim-|vim\\.|select|webkit|github|ftp_|ftp-|ftp\\.|creator|mongoose|led_|led-|led\\.|movie|currently|pdf_|pdf-|pdf\\.|load|markdown|phalcon|input|custom|atom|oracle|phonegap|ubuntu|great|rdf_|rdf-|rdf\\.|popcorn|firefox|zip_|zip-|zip\\.|cuda|dotfile|static|openwrt|viewer|powered|graphic|les_|les-|les\\.|doe_|doe-|doe\\.|maven|word|eclipse|lab_|lab-|lab\\.|hacking|steam|analytic|option|abstract|archive|reality|switcher|club|write|kafka|arduino|angular|online|title|don\'t|contao|notice|analyzer|learning|zend|external|staging|busines|tdd_|tdd-|tdd\\.|scanner|building|snippet|modular|bower|stm_|stm-|stm\\.|lib_|lib-|lib\\.|alpha|mobile|clean|linux|nginx|manifest|some|raspberry|gnome|ide_|ide-|ide\\.|block|statistic|info|drag|youtube|koan|facebook|paperclip|art_|art-|art\\.|quality|tab_|tab-|tab\\.|need|dojo|shield|computer|stat|state|twitter|utility|converter|hosting|devise|liferay|updated|force|tip_|tip-|tip\\.|behavior|active|call|answer|deck|better|principle|ches|bar_|bar-|bar\\.|reddit|three|haxe|just|plug-in|agile|manual|tetri|super|beta|parsing|doctrine|minecraft|useful|perl|sharing|agent|switch|view|dash|channel|repo|pebble|profiler|warning|cluster|running|markup|evented|mod_|mod-|mod\\.|share|csv_|csv-|csv\\.|response|good|house|connect|built|build|find|ipython|webgl|big_|big-|big\\.|google|scala|sdl_|sdl-|sdl\\.|sdk_|sdk-|sdk\\.|native|day_|day-|day\\.|puppet|text|routing|helper|linkedin|crawler|host|guard|merchant|poker|over|writing|free|classe|component|craft|nodej|phoenix|longer|quick|lazy|memory|clone|hacker|middleman|factory|motion|multiple|tornado|hack|ssh_|ssh-|ssh\\.|review|vimrc|driver|driven|blog|particle|table|intro|importer|thrift|xmpp|framework|refresh|react|font|librarie|variou|formatter|analysi|karma|scroll|tut_|tut-|tut\\.|apple|tag_|tag-|tag\\.|tab_|tab-|tab\\.|category|ionic|cache|homebrew|reverse|english|getting|shipping|clojure|boot|book|branch|combination|combo)) 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 (?!(^0x0*|^pub)|.*\\.(bin|json|exe)$|.*(?i)(Client|Factory)$|(^__[A-Za-z]+__$)|^(12345|abcd)|^\\d+(\\.\\d+)?$) 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 entropy (?i)(?:key|api|token|secret|client|passwd|password|auth|access)(?:[0-9a-z\\-_\\t.]{0,20})(?:[\\s|\']|[\\s|"]){0,3}(?:=|>|:=|\\|\\|:|<=|=>|:)(?:\'|@\\"|\\"|\\s|=|\\x60){0,5}(?!([a-z]+\\.[a-zA-Z]+)|.*(\\d{4}-\\d{2}-\\d{2})|:*(?!("|\'))[0-9A-Za-z]+\\.[0-9A-Za-z]+,|[A-Z]+_[A-Z]+_)(?P<CONTENT>[0-9a-z\\-_.=\\~@]{10,150})(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$) (\\d\\.\\d\\.\\d-}|([\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3})|(\\w)\\1{5}|(?i)keywords|xxxx|eeeeeeee|0000|\\*\\*\\*|example|test|author=|author("|\')|preview|[A-Z]+_KEY|[.]value|[.]key|-\\d\\.\\d\\.) (\\w|\\.)\\1{5} .*((?i)omitted|arn:aws|(?i)(pub.*key|public.*key)|(?i)clientToken|symbol|cache|author\\.).*', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.generic-api-key') match_id = e568a8303898f65c43738ce9b467b3811ade960877866255d500f56f4cf5486be06aa8df9e775fee32241e3dbf0b475534fcd8256ff0310b842e905efeecb8ee_0
2025-06-02 17:44:54,702 - semgrep.rule_match - DEBUG - match_key = ('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 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 (?!(?i).*(client|endpoint|vpn|_ec2_|aws_|authorize|author|define|config|credential|setting|sample|xxxxxx|000000|buffer|delete|aaaaaa|fewfwef|getenv|env_|system|example|ecdsa|sha256|sha1|sha2|md5|alert|wizard|target|onboard|welcome|page|exploit|experiment|expire|rabbitmq|scraper|widget|music|dns_|dns-|yahoo|want|json|action|script|fix_|fix-|develop|compas|stripe|service|master|metric|tech|gitignore|rich|open|stack|irc_|irc-|sublime|kohana|has_|has-|fabric|wordpres|role|osx_|osx-|boost|addres|queue|working|sandbox|internet|print|vision|tracking|being|generator|traffic|world|pull|rust|watcher|small|auth|full|hash|more|install|auto|complete|learn|paper|installer|research|acces|last|binding|spine|into|chat|algorithm|resource|uploader|video|maker|next|proc|lock|robot|snake|patch|matrix|drill|terminal|term|stuff|genetic|generic|identity|audit|pattern|audio|web_|web-|crud|problem|statu|cms-|cms_|arch|coffee|workflow|changelog|another|uiview|content|kitchen|gnu_|gnu-|gnu\\.|conf|couchdb|client|opencv|rendering|update|concept|varnish|gui_|gui-|gui\\.|version|shared|extra|product|still|not_|not-|not\\.|drop|ring|png_|png-|png\\.|actively|import|output|backup|start|embedded|registry|pool|semantic|instagram|bash|system|ninja|drupal|jquery|polyfill|physic|league|guide|pack|synopsi|sketch|injection|svg_|svg-|svg\\.|friendly|wave|convert|manage|camera|link|slide|timer|wrapper|gallery|url_|url-|url\\.|todomvc|requirej|party|http|payment|async|library|home|coco|gaia|display|universal|func|metadata|hipchat|under|room|config|personal|realtime|resume|database|testing|tiny|basic|forum|meetup|yet_|yet-|yet\\.|cento|dead|fluentd|editor|utilitie|run_|run-|run\\.|box_|box-|box\\.|bot_|bot-|bot\\.|making|sample|group|monitor|ajax|parallel|cassandra|ultimate|site|get_|get-|get\\.|gen_|gen-|gen\\.|gem_|gem-|gem\\.|extended|image|knife|asset|nested|zero|plugin|bracket|mule|mozilla|number|act_|act-|act\\.|map_|map-|map\\.|micro|debug|openshift|chart|expres|backend|task|source|translate|jbos|composer|sqlite|profile|mustache|mqtt|yeoman|have|builder|smart|like|oauth|school|guideline|captcha|filter|bitcoin|bridge|color|toolbox|discovery|new_|new-|new\\.|dashboard|when|setting|level|post|standard|port|platform|yui_|yui-|yui\\.|grunt|animation|haskell|icon|latex|cheat|lua_|lua-|lua\\.|gulp|case|author|without|simulator|wifi|directory|lisp|list|flat|adventure|story|storm|gpu_|gpu-|gpu\\.|store|caching|attention|solr|logger|demo|shortener|hadoop|finder|phone|pipeline|range|textmate|showcase|app_|app-|app\\.|idiomatic|edit|our_|our-|our\\.|out_|out-|out\\.|sentiment|linked|why_|why-|why\\.|local|cube|gmail|job_|job-|job\\.|rpc_|rpc-|rpc\\.|contest|tcp_|tcp-|tcp\\.|usage|buildout|weather|transfer|automated|sphinx|issue|sas_|sas-|sas\\.|parallax|jasmine|addon|machine|solution|dsl_|dsl-|dsl\\.|episode|menu|theme|best|adapter|debugger|chrome|tutorial|life|step|people|joomla|paypal|developer|solver|team|current|love|visual|date|data|canva|container|future|xml_|xml-|xml\\.|twig|nagio|spatial|original|sync|archived|refinery|science|mapping|gitlab|play|ext_|ext-|ext\\.|session|impact|set_|set-|set\\.|see_|see-|see\\.|migration|commit|community|shopify|what\'|cucumber|statamic|mysql|location|tower|line|code|amqp|hello|send|index|high|notebook|alloy|python|field|document|soap|edition|email|php_|php-|php\\.|command|transport|official|upload|study|secure|angularj|akka|scalable|package|request|con_|con-|con\\.|flexible|security|comment|module|flask|graph|flash|apache|change|window|space|lambda|sheet|bookmark|carousel|friend|objective|jekyll|bootstrap|first|article|gwt_|gwt-|gwt\\.|classic|media|websocket|touch|desktop|real|read|recorder|moved|storage|validator|add-on|pusher|scs_|scs-|scs\\.|inline|asp_|asp-|asp\\.|timeline|base|encoding|ffmpeg|kindle|tinymce|pretty|jpa_|jpa-|jpa\\.|used|user|required|webhook|download|resque|espresso|cloud|mongo|benchmark|pure|cakephp|modx|mode|reactive|fuel|written|flickr|mail|brunch|meteor|dynamic|neo_|neo-|neo\\.|new_|new-|new\\.|net_|net-|net\\.|typo|type|keyboard|erlang|adobe|logging|ckeditor|message|iso_|iso-|iso\\.|hook|ldap|folder|reference|railscast|www_|www-|www\\.|tracker|azure|fork|form|digital|exporter|skin|string|template|designer|gollum|fluent|entity|language|alfred|summary|wiki|kernel|calendar|plupload|symfony|foundry|remote|talk|search|dev_|dev-|dev\\.|del_|del-|del\\.|token|idea|sencha|selector|interface|create|fun_|fun-|fun\\.|groovy|query|grail|red_|red-|red\\.|laravel|monkey|slack|supported|instant|value|center|latest|work|but_|but-|but\\.|bug_|bug-|bug\\.|virtual|tweet|statsd|studio|path|real-time|frontend|notifier|coding|tool|firmware|flow|random|mediawiki|bosh|been|beer|lightbox|theory|origin|redmine|hub_|hub-|hub\\.|require|pro_|pro-|pro\\.|ant_|ant-|ant\\.|any_|any-|any\\.|recipe|closure|mapper|event|todo|model|redi|provider|rvm_|rvm-|rvm\\.|program|memcached|rail|silex|foreman|activity|license|strategy|batch|streaming|fast|use_|use-|use\\.|usb_|usb-|usb\\.|impres|academy|slider|please|layer|cros|now_|now-|now\\.|miner|extension|own_|own-|own\\.|app_|app-|app\\.|debian|symphony|example|feature|serie|tree|project|runner|entry|leetcode|layout|webrtc|logic|login|worker|toolkit|mocha|support|back|inside|device|jenkin|contact|fake|awesome|ocaml|bit_|bit-|bit\\.|drive|screen|prototype|gist|binary|nosql|rest|overview|dart|dark|emac|mongoid|solarized|homepage|emulator|commander|django|yandex|gradle|xcode|writer|crm_|crm-|crm\\.|jade|startup|error|using|format|name|spring|parser|scratch|magic|try_|try-|try\\.|rack|directive|challenge|slim|counter|element|chosen|doc_|doc-|doc\\.|meta|should|button|packet|stream|hardware|android|infinite|password|software|ghost|xamarin|spec|chef|interview|hubot|mvc_|mvc-|mvc\\.|exercise|leaflet|launcher|air_|air-|air\\.|photo|board|boxen|way_|way-|way\\.|computing|welcome|notepad|portfolio|cat_|cat-|cat\\.|can_|can-|can\\.|magento|yaml|domain|card|yii_|yii-|yii\\.|checker|browser|upgrade|only|progres|aura|ruby_|ruby-|ruby\\.|polymer|util|lite|hackathon|rule|log_|log-|log\\.|opengl|stanford|skeleton|history|inspector|help|soon|selenium|lab_|lab-|lab\\.|scheme|schema|look|ready|leveldb|docker|game|minimal|logstash|messaging|within|heroku|mongodb|kata|suite|picker|win_|win-|win\\.|wip_|wip-|wip\\.|panel|started|starter|front-end|detector|deploy|editing|based|admin|capture|spree|page|bundle|goal|rpg_|rpg-|rpg\\.|setup|side|mean|reader|cookbook|mini|modern|seed|dom_|dom-|dom\\.|doc_|doc-|doc\\.|dot_|dot-|dot\\.|syntax|sugar|loader|website|make|kit_|kit-|kit\\.|protocol|human|daemon|golang|manager|countdown|connector|swagger|map_|map-|map\\.|mac_|mac-|mac\\.|man_|man-|man\\.|orm_|orm-|orm\\.|org_|org-|org\\.|little|zsh_|zsh-|zsh\\.|shop|show|workshop|money|grid|server|octopres|svn_|svn-|svn\\.|ember|embed|general|file|important|dropbox|portable|public|docpad|fish|sbt_|sbt-|sbt\\.|done|para|network|common|readme|popup|simple|purpose|mirror|single|cordova|exchange|object|design|gateway|account|lamp|intellij|math|mit_|mit-|mit\\.|control|enhanced|emitter|multi|add_|add-|add\\.|about|socket|preview|vagrant|cli_|cli-|cli\\.|powerful|top_|top-|top\\.|radio|watch|fluid|amazon|report|couchbase|automatic|detection|sprite|pyramid|portal|advanced|plu_|plu-|plu\\.|runtime|git_|git-|git\\.|uri_|uri-|uri\\.|haml|node|sql_|sql-|sql\\.|cool|core|obsolete|handler|iphone|extractor|array|copy|nlp_|nlp-|nlp\\.|reveal|pop_|pop-|pop\\.|engine|parse|check|html|nest|all_|all-|all\\.|chinese|buildpack|what|tag_|tag-|tag\\.|proxy|style|cookie|feed|restful|compiler|creating|prelude|context|java|rspec|mock|backbone|light|spotify|flex|related|shell|which|clas|webapp|swift|ansible|unity|console|tumblr|export|campfire|conway\'|made|riak|hero|here|unix|unit|glas|smtp|how_|how-|how\\.|hot_|hot-|hot\\.|debug|release|diff|player|easy|right|old_|old-|old\\.|animate|time|push|explorer|course|training|nette|router|draft|structure|note|salt|where|spark|trello|power|method|social|via_|via-|via\\.|vim_|vim-|vim\\.|select|webkit|github|ftp_|ftp-|ftp\\.|creator|mongoose|led_|led-|led\\.|movie|currently|pdf_|pdf-|pdf\\.|load|markdown|phalcon|input|custom|atom|oracle|phonegap|ubuntu|great|rdf_|rdf-|rdf\\.|popcorn|firefox|zip_|zip-|zip\\.|cuda|dotfile|static|openwrt|viewer|powered|graphic|les_|les-|les\\.|doe_|doe-|doe\\.|maven|word|eclipse|lab_|lab-|lab\\.|hacking|steam|analytic|option|abstract|archive|reality|switcher|club|write|kafka|arduino|angular|online|title|don\'t|contao|notice|analyzer|learning|zend|external|staging|busines|tdd_|tdd-|tdd\\.|scanner|building|snippet|modular|bower|stm_|stm-|stm\\.|lib_|lib-|lib\\.|alpha|mobile|clean|linux|nginx|manifest|some|raspberry|gnome|ide_|ide-|ide\\.|block|statistic|info|drag|youtube|koan|facebook|paperclip|art_|art-|art\\.|quality|tab_|tab-|tab\\.|need|dojo|shield|computer|stat|state|twitter|utility|converter|hosting|devise|liferay|updated|force|tip_|tip-|tip\\.|behavior|active|call|answer|deck|better|principle|ches|bar_|bar-|bar\\.|reddit|three|haxe|just|plug-in|agile|manual|tetri|super|beta|parsing|doctrine|minecraft|useful|perl|sharing|agent|switch|view|dash|channel|repo|pebble|profiler|warning|cluster|running|markup|evented|mod_|mod-|mod\\.|share|csv_|csv-|csv\\.|response|good|house|connect|built|build|find|ipython|webgl|big_|big-|big\\.|google|scala|sdl_|sdl-|sdl\\.|sdk_|sdk-|sdk\\.|native|day_|day-|day\\.|puppet|text|routing|helper|linkedin|crawler|host|guard|merchant|poker|over|writing|free|classe|component|craft|nodej|phoenix|longer|quick|lazy|memory|clone|hacker|middleman|factory|motion|multiple|tornado|hack|ssh_|ssh-|ssh\\.|review|vimrc|driver|driven|blog|particle|table|intro|importer|thrift|xmpp|framework|refresh|react|font|librarie|variou|formatter|analysi|karma|scroll|tut_|tut-|tut\\.|apple|tag_|tag-|tag\\.|tab_|tab-|tab\\.|category|ionic|cache|homebrew|reverse|english|getting|shipping|clojure|boot|book|branch|combination|combo)) 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 (?!(^0x0*|^pub)|.*\\.(bin|json|exe)$|.*(?i)(Client|Factory)$|(^__[A-Za-z]+__$)|^(12345|abcd)|^\\d+(\\.\\d+)?$) 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 entropy (?i)(?:key|api|token|secret|client|passwd|password|auth|access)(?:[0-9a-z\\-_\\t.]{0,20})(?:[\\s|\']|[\\s|"]){0,3}(?:=|>|:=|\\|\\|:|<=|=>|:)(?:\'|@\\"|\\"|\\s|=|\\x60){0,5}(?!([a-z]+\\.[a-zA-Z]+)|.*(\\d{4}-\\d{2}-\\d{2})|:*(?!("|\'))[0-9A-Za-z]+\\.[0-9A-Za-z]+,|[A-Z]+_[A-Z]+_)(?P<CONTENT>[0-9a-z\\-_.=\\~@]{10,150})(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$) (\\d\\.\\d\\.\\d-}|([\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3})|(\\w)\\1{5}|(?i)keywords|xxxx|eeeeeeee|0000|\\*\\*\\*|example|test|author=|author("|\')|preview|[A-Z]+_KEY|[.]value|[.]key|-\\d\\.\\d\\.) (\\w|\\.)\\1{5} .*((?i)omitted|arn:aws|(?i)(pub.*key|public.*key)|(?i)clientToken|symbol|cache|author\\.).*', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.generic-api-key') match_id = e568a8303898f65c43738ce9b467b3811ade960877866255d500f56f4cf5486be06aa8df9e775fee32241e3dbf0b475534fcd8256ff0310b842e905efeecb8ee_0
2025-06-02 17:44:54,703 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = bad1f9a65aa6ffc34be3c9b4eba6907d800b32c637f3d71e87e49d7cffdabc3ed7ab0f0a157e893d0a52f84d44756da0d75d5fc2772bb32547c4b6cef2ada4cd_0
2025-06-02 17:44:54,704 - semgrep.rule_match - DEBUG - match_key = ('(?i)\\b(sk-[a-zA-Z0-9_-]+)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = 3e50d6ea8a500614622e79a105f3dfbaa997e770c8cb4a1e768cc27a408d498a88b2f636af438b2512d373fe2881663b8e3f9c7be997c47d38b85d430493174e_0
2025-06-02 17:44:54,705 - semgrep.rule_match - DEBUG - match_key = ('(?i)\\b(sk-[a-zA-Z0-9_-]+)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = 3e50d6ea8a500614622e79a105f3dfbaa997e770c8cb4a1e768cc27a408d498a88b2f636af438b2512d373fe2881663b8e3f9c7be997c47d38b85d430493174e_0
2025-06-02 17:44:54,706 - semgrep.rule_match - DEBUG - match_key = ('(?i)\\b(sk-[a-zA-Z0-9_-]+)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = 3e50d6ea8a500614622e79a105f3dfbaa997e770c8cb4a1e768cc27a408d498a88b2f636af438b2512d373fe2881663b8e3f9c7be997c47d38b85d430493174e_0
2025-06-02 17:44:54,708 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-02 17:44:54,709 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,711 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,712 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,714 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-02 17:44:54,715 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,717 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,718 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,720 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-02 17:44:54,722 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,723 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,725 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_1
2025-06-02 17:44:54,727 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-02 17:44:54,728 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,730 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,731 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_1
2025-06-02 17:44:54,733 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-02 17:44:54,734 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,736 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,738 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_2
2025-06-02 17:44:54,739 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-02 17:44:54,740 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,742 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,743 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_2
2025-06-02 17:44:54,745 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-02 17:44:54,746 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,748 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,749 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_3
2025-06-02 17:44:54,750 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-02 17:44:54,752 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,753 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,754 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_3
2025-06-02 17:44:54,755 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-02 17:44:54,757 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,758 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,759 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_4
2025-06-02 17:44:54,760 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-02 17:44:54,762 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,763 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,764 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_4
2025-06-02 17:44:54,766 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-02 17:44:54,767 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,769 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,770 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_5
2025-06-02 17:44:54,772 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-02 17:44:54,773 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,775 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,776 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_5
2025-06-02 17:44:54,778 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-02 17:44:54,780 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,782 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,784 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_6
2025-06-02 17:44:54,786 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-02 17:44:54,787 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,788 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,790 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_6
2025-06-02 17:44:54,791 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-02 17:44:54,793 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,794 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-02 17:44:54,796 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_7
2025-06-02 17:44:54,798 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-02 17:44:54,799 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,801 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-02 17:44:54,802 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_7
2025-06-02 17:44:54,804 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0ee74fd49637bebe183eca7188dbde26e386314e62cc2e7ba1ee60b377b638243fcd84e6c6fa04886198ccacfa6a711bfbcc61a28f9ddc913d5b3c53083cbc90_0
2025-06-02 17:44:54,805 - semgrep.rule_match - DEBUG - match_key = ('\n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" (?is).*integrity= (google-analytics\\.com|fonts\\.googleapis\\.com|fonts\\.gstatic\\.com|googletagmanager\\.com) .*rel\\s*=\\s*[\'"]?preconnect.* href="... :// ..." href="//..." href=\'... :// ...\' href=\'//...\' src="... :// ..." src="//..." src=\'... :// ...\' src=\'//...\' <link \n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" > <script \n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" >...</script>', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0c462f34c2e5c0115c98099a09d5fde04ba5aac86f96eda89dc79e1650b67f17507e9f0d764590150610e5e58e4f87c72431ae06d0266dd4ca65d989374ab76e_0
2025-06-02 17:44:54,806 - semgrep.rule_match - DEBUG - match_key = ('\n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" (?is).*integrity= (google-analytics\\.com|fonts\\.googleapis\\.com|fonts\\.gstatic\\.com|googletagmanager\\.com) .*rel\\s*=\\s*[\'"]?preconnect.* href="... :// ..." href="//..." href=\'... :// ...\' href=\'//...\' src="... :// ..." src="//..." src=\'... :// ...\' src=\'//...\' <link \n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" > <script \n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" >...</script>', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0c462f34c2e5c0115c98099a09d5fde04ba5aac86f96eda89dc79e1650b67f17507e9f0d764590150610e5e58e4f87c72431ae06d0266dd4ca65d989374ab76e_0
2025-06-02 17:44:54,806 - semgrep.rule_match - DEBUG - match_key = ('\n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" (?is).*integrity= (google-analytics\\.com|fonts\\.googleapis\\.com|fonts\\.gstatic\\.com|googletagmanager\\.com) .*rel\\s*=\\s*[\'"]?preconnect.* href="... :// ..." href="//..." href=\'... :// ...\' href=\'//...\' src="... :// ..." src="//..." src=\'... :// ...\' src=\'//...\' <link \n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" > <script \n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" >...</script>', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0c462f34c2e5c0115c98099a09d5fde04ba5aac86f96eda89dc79e1650b67f17507e9f0d764590150610e5e58e4f87c72431ae06d0266dd4ca65d989374ab76e_0
2025-06-02 17:44:54,807 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.possible-nginx-h2c-smuggling') match_id = 660e4ea421c5e211d693000daff047303b04d9b1dc85a0a478c9d6f41550386d7a0a5f9668da89e45a82923879ec95fb56615405245166dd66e1bff5db071fba_0
2025-06-02 17:44:54,807 - semgrep.rule_match - DEBUG - match_key = ('location ... {\n  ...\n}\n proxy_http_version 1.1 ...;\n...\nproxy_set_header Upgrade ...;\n...\nproxy_set_header Connection ...;\n proxy_set_header Upgrade ...;\n...\nproxy_http_version 1.1 ...;\n...\nproxy_set_header Connection ...;\n proxy_set_header Upgrade ...;\n...\nproxy_set_header Connection ...;\n...\nproxy_http_version 1.1 ...;\n', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.possible-nginx-h2c-smuggling') match_id = ae6907400de063e188ef4540fa76d34ea58319f2c3b5774a76ec4dda7dfde9c9f12e9f91507a7d9cbd5a57d2a3afc7559f097362f1431347b0cbde5079ab1a53_0
2025-06-02 17:44:54,808 - semgrep.rule_match - DEBUG - match_key = ('location ... {\n  ...\n}\n proxy_http_version 1.1 ...;\n...\nproxy_set_header Upgrade ...;\n...\nproxy_set_header Connection ...;\n proxy_set_header Upgrade ...;\n...\nproxy_http_version 1.1 ...;\n...\nproxy_set_header Connection ...;\n proxy_set_header Upgrade ...;\n...\nproxy_set_header Connection ...;\n...\nproxy_http_version 1.1 ...;\n', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.possible-nginx-h2c-smuggling') match_id = ae6907400de063e188ef4540fa76d34ea58319f2c3b5774a76ec4dda7dfde9c9f12e9f91507a7d9cbd5a57d2a3afc7559f097362f1431347b0cbde5079ab1a53_0
2025-06-02 17:44:54,809 - semgrep.rule_match - DEBUG - match_key = ('location ... {\n  ...\n}\n proxy_http_version 1.1 ...;\n...\nproxy_set_header Upgrade ...;\n...\nproxy_set_header Connection ...;\n proxy_set_header Upgrade ...;\n...\nproxy_http_version 1.1 ...;\n...\nproxy_set_header Connection ...;\n proxy_set_header Upgrade ...;\n...\nproxy_set_header Connection ...;\n...\nproxy_http_version 1.1 ...;\n', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.possible-nginx-h2c-smuggling') match_id = ae6907400de063e188ef4540fa76d34ea58319f2c3b5774a76ec4dda7dfde9c9f12e9f91507a7d9cbd5a57d2a3afc7559f097362f1431347b0cbde5079ab1a53_0
2025-06-02 17:44:54,809 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.request-host-used') match_id = 327a18752ef8dd885cb20efad7118994364aa67a9696b0144c9d9d3b222b9dc96375e3d614939649799ce0b488347523b54919c9d313c25997ffc4672be41eaa_0
2025-06-02 17:44:54,810 - semgrep.rule_match - DEBUG - match_key = ('$host $http_host', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.request-host-used') match_id = 5ac33422430ce88039eda001e6e2112be87ee0b4840d6dee322e36fd258af6aff9d5f10970384d1948b4b80ff92a7df8ac5ce6067eac11b7dbcb557f5bc5ccc9_0
2025-06-02 17:44:54,810 - semgrep.rule_match - DEBUG - match_key = ('$host $http_host', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.request-host-used') match_id = 5ac33422430ce88039eda001e6e2112be87ee0b4840d6dee322e36fd258af6aff9d5f10970384d1948b4b80ff92a7df8ac5ce6067eac11b7dbcb557f5bc5ccc9_0
2025-06-02 17:44:54,811 - semgrep.rule_match - DEBUG - match_key = ('$host $http_host', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.request-host-used') match_id = 5ac33422430ce88039eda001e6e2112be87ee0b4840d6dee322e36fd258af6aff9d5f10970384d1948b4b80ff92a7df8ac5ce6067eac11b7dbcb557f5bc5ccc9_0
2025-06-02 17:44:54,812 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/components/flashcards/AiFlashcardGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 48b9a704cbda6c074e8b6c11305b58cc36af2e309003a24ba9e25b89f56d6b9be93e8d88150abefc64ad5f26214029681c8abc74bf654da56bdd0bd4ab0406cb_0
2025-06-02 17:44:54,812 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching document docId:,err,...)\n console.error(`Error fetching document docId:,err,...)\n', PosixPath('client/src/components/flashcards/AiFlashcardGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = bd1fac6af7e75e1d2b23d59c800579bdf1fb88589236269386cb11c2e882820a2cb0e9cec992aeeefff947284a85cb832b2cd32eca3cab650662a411e3c383d1_0
2025-06-02 17:44:54,813 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching document docId:,err,...)\n console.error(`Error fetching document docId:,err,...)\n', PosixPath('client/src/components/flashcards/AiFlashcardGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = bd1fac6af7e75e1d2b23d59c800579bdf1fb88589236269386cb11c2e882820a2cb0e9cec992aeeefff947284a85cb832b2cd32eca3cab650662a411e3c383d1_0
2025-06-02 17:44:54,814 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching document docId:,err,...)\n console.error(`Error fetching document docId:,err,...)\n', PosixPath('client/src/components/flashcards/AiFlashcardGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = bd1fac6af7e75e1d2b23d59c800579bdf1fb88589236269386cb11c2e882820a2cb0e9cec992aeeefff947284a85cb832b2cd32eca3cab650662a411e3c383d1_0
2025-06-02 17:44:54,815 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/components/flashcards/FlashcardSetList.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 1ff6a5bc184a481bee45d766cfe0da40e342109310c886511f36aad6d5ce819902a67b91b18bb42f69e54005633ce497759588ca17c7bf1308a2e7e3be02d007_0
2025-06-02 17:44:54,815 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error counting cards for set set.id: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error counting cards for set set.id:,countError,...)\n console.error(`Error counting cards for set set.id:,countError,...)\n', PosixPath('client/src/components/flashcards/FlashcardSetList.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = b92ef4ad7962908320f541ce4675d29514d1fde67db732df8e406588322b43bb0b90b535a9e73b23175b9fc8cbe1cec0953b08d0bfac74d590345463da20dc91_0
2025-06-02 17:44:54,816 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error counting cards for set set.id: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error counting cards for set set.id:,countError,...)\n console.error(`Error counting cards for set set.id:,countError,...)\n', PosixPath('client/src/components/flashcards/FlashcardSetList.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = b92ef4ad7962908320f541ce4675d29514d1fde67db732df8e406588322b43bb0b90b535a9e73b23175b9fc8cbe1cec0953b08d0bfac74d590345463da20dc91_0
2025-06-02 17:44:54,816 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error counting cards for set set.id: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error counting cards for set set.id:,countError,...)\n console.error(`Error counting cards for set set.id:,countError,...)\n', PosixPath('client/src/components/flashcards/FlashcardSetList.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = b92ef4ad7962908320f541ce4675d29514d1fde67db732df8e406588322b43bb0b90b535a9e73b23175b9fc8cbe1cec0953b08d0bfac74d590345463da20dc91_0
2025-06-02 17:44:54,818 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/components/quiz/AiQuestionGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e4ab50d8f5c45b8520c6f9625b520b8577b79a88fdf0b28ec4fd75e6d460029dc55393b6555ff65507139fd4df1ca6194fa847f7b10ddfb364f0edd6c0efff38_0
2025-06-02 17:44:54,819 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Added questionsToAdd.length questions to quiz selectedQuizId. Response: $UTIL = require(\'util\')\n...\n $UTIL.format(`Added questionsToAdd.length questions to quiz selectedQuizId. Response:,addedQuestions,...)\n console.log(`Added questionsToAdd.length questions to quiz selectedQuizId. Response:,addedQuestions,...)\n', PosixPath('client/src/components/quiz/AiQuestionGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = aa3c9374ac47fd0d72505d61e23d918c36c3a7f19cf5f31a42e54f2cc924153df8191e5bf4dd4fa3de008288263b1645bd0365046c96646cb737a3917f1cb34c_0
2025-06-02 17:44:54,820 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Added questionsToAdd.length questions to quiz selectedQuizId. Response: $UTIL = require(\'util\')\n...\n $UTIL.format(`Added questionsToAdd.length questions to quiz selectedQuizId. Response:,addedQuestions,...)\n console.log(`Added questionsToAdd.length questions to quiz selectedQuizId. Response:,addedQuestions,...)\n', PosixPath('client/src/components/quiz/AiQuestionGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = aa3c9374ac47fd0d72505d61e23d918c36c3a7f19cf5f31a42e54f2cc924153df8191e5bf4dd4fa3de008288263b1645bd0365046c96646cb737a3917f1cb34c_0
2025-06-02 17:44:54,821 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Added questionsToAdd.length questions to quiz selectedQuizId. Response: $UTIL = require(\'util\')\n...\n $UTIL.format(`Added questionsToAdd.length questions to quiz selectedQuizId. Response:,addedQuestions,...)\n console.log(`Added questionsToAdd.length questions to quiz selectedQuizId. Response:,addedQuestions,...)\n', PosixPath('client/src/components/quiz/AiQuestionGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = aa3c9374ac47fd0d72505d61e23d918c36c3a7f19cf5f31a42e54f2cc924153df8191e5bf4dd4fa3de008288263b1645bd0365046c96646cb737a3917f1cb34c_0
2025-06-02 17:44:54,824 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/pages/FlashcardsPage.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = b207cb55c60a0222cf29d53226a7984cc7f97441a09bbc164a4b2bf4d1affd05d64d605a6abf799846d69de9e7aa4f022d26a501823580e64d773d28a692100b_0
2025-06-02 17:44:54,826 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching document docId:,err,...)\n console.error(`Error fetching document docId:,err,...)\n', PosixPath('client/src/pages/FlashcardsPage.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = f3bfd07b56fa42130ffd546148e5c7a0d8bc75a38afc29cf4f0e3401196fcd6e352f77bf69fb792a956da31a76f367e56b53bc191bd269133280ba4a83f2f268_0
2025-06-02 17:44:54,827 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching document docId:,err,...)\n console.error(`Error fetching document docId:,err,...)\n', PosixPath('client/src/pages/FlashcardsPage.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = f3bfd07b56fa42130ffd546148e5c7a0d8bc75a38afc29cf4f0e3401196fcd6e352f77bf69fb792a956da31a76f367e56b53bc191bd269133280ba4a83f2f268_0
2025-06-02 17:44:54,828 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching document docId:,err,...)\n console.error(`Error fetching document docId:,err,...)\n', PosixPath('client/src/pages/FlashcardsPage.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = f3bfd07b56fa42130ffd546148e5c7a0d8bc75a38afc29cf4f0e3401196fcd6e352f77bf69fb792a956da31a76f367e56b53bc191bd269133280ba4a83f2f268_0
2025-06-02 17:44:54,829 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/index.ts'), 'config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write') match_id = f96200e70d96e050ff0d48a206612e913a78ae8d9482c012ee690fafcbb91b6cf36929fc1b7be05f92e44386866d8f5610990d984b647449adbeaaf1cabddf9c_0
2025-06-02 17:44:54,830 - semgrep.rule_match - DEBUG - match_key = ("$APP.$METHOD(..., function $FUNC(req, res) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n})\n $APP.$METHOD(..., function $FUNC(req, res) {\n    ...\n    res.set('$TYPE')\n})\n $APP.$METHOD(..., function $FUNC(req, res) {...}) $METHOD ^(get|post|put|head|delete|options) function ... (req, res) {...} function ... (req, res, next) {...} req.body req.params req.query function ... (req, res) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n function ... (req, res) {\n    ...\n    res.set('$TYPE')\n}\n function ... (req, res, next) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n function ... (req, res, next) {\n    ...\n    res.set('$TYPE')\n}\n req ({ req }: Request,res: Response) => {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n ({ req }: Request,res: Response) => {...}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{...}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{\n    ...\n    res.set('$TYPE')\n}\n body params query value res. ... .set('...'). ... .send(value) res. ... .type('...'). ... .send(value) res.$METHOD({ ... }) res.send(value) res.write(value) function ... (..., res,...) {...}", PosixPath('server/index.ts'), 'config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write') match_id = f36b6aa5df41601445443e25ee24c78dc3b1ab71697614b46b6f62d5e2686326d9ed73a5abd635b3703de01a746e913adc48c823e3572792b885c9da14f29098_0
2025-06-02 17:44:54,832 - semgrep.rule_match - DEBUG - match_key = ("$APP.$METHOD(..., function $FUNC(req, res) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n})\n $APP.$METHOD(..., function $FUNC(req, res) {\n    ...\n    res.set('$TYPE')\n})\n $APP.$METHOD(..., function $FUNC(req, res) {...}) $METHOD ^(get|post|put|head|delete|options) function ... (req, res) {...} function ... (req, res, next) {...} req.body req.params req.query function ... (req, res) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n function ... (req, res) {\n    ...\n    res.set('$TYPE')\n}\n function ... (req, res, next) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n function ... (req, res, next) {\n    ...\n    res.set('$TYPE')\n}\n req ({ req }: Request,res: Response) => {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n ({ req }: Request,res: Response) => {...}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{...}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{\n    ...\n    res.set('$TYPE')\n}\n body params query value res. ... .set('...'). ... .send(value) res. ... .type('...'). ... .send(value) res.$METHOD({ ... }) res.send(value) res.write(value) function ... (..., res,...) {...}", PosixPath('server/index.ts'), 'config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write') match_id = f36b6aa5df41601445443e25ee24c78dc3b1ab71697614b46b6f62d5e2686326d9ed73a5abd635b3703de01a746e913adc48c823e3572792b885c9da14f29098_0
2025-06-02 17:44:54,833 - semgrep.rule_match - DEBUG - match_key = ("$APP.$METHOD(..., function $FUNC(req, res) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n})\n $APP.$METHOD(..., function $FUNC(req, res) {\n    ...\n    res.set('$TYPE')\n})\n $APP.$METHOD(..., function $FUNC(req, res) {...}) $METHOD ^(get|post|put|head|delete|options) function ... (req, res) {...} function ... (req, res, next) {...} req.body req.params req.query function ... (req, res) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n function ... (req, res) {\n    ...\n    res.set('$TYPE')\n}\n function ... (req, res, next) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n function ... (req, res, next) {\n    ...\n    res.set('$TYPE')\n}\n req ({ req }: Request,res: Response) => {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n ({ req }: Request,res: Response) => {...}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{...}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{\n    ...\n    res.set('$TYPE')\n}\n body params query value res. ... .set('...'). ... .send(value) res. ... .type('...'). ... .send(value) res.$METHOD({ ... }) res.send(value) res.write(value) function ... (..., res,...) {...}", PosixPath('server/index.ts'), 'config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write') match_id = f36b6aa5df41601445443e25ee24c78dc3b1ab71697614b46b6f62d5e2686326d9ed73a5abd635b3703de01a746e913adc48c823e3572792b885c9da14f29098_0
2025-06-02 17:44:54,834 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = 30ad92c285f157dfcf606c2e424ebbfceda68d2d91bfdf00791f0871a65bd55b4c7a47ccfd2888717d6c0aefc93ee200eb4c55ad7f2d3e12cad8034e9e890569_0
2025-06-02 17:44:54,834 - semgrep.rule_match - DEBUG - match_key = ('crypto.createCipher(...)\n crypto.createDecipher(...)\n', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = 6d3eb22c4e8a0db496bdfaf8f1f1e5e93954625c7369acb8d3d89700a4bd63ce114e267a86abbcf7b08abc45e3bda8e0e9cd3918582a80ff8f36b301e337efc7_0
2025-06-02 17:44:54,835 - semgrep.rule_match - DEBUG - match_key = ('crypto.createCipher(...)\n crypto.createDecipher(...)\n', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = 6d3eb22c4e8a0db496bdfaf8f1f1e5e93954625c7369acb8d3d89700a4bd63ce114e267a86abbcf7b08abc45e3bda8e0e9cd3918582a80ff8f36b301e337efc7_0
2025-06-02 17:44:54,835 - semgrep.rule_match - DEBUG - match_key = ('crypto.createCipher(...)\n crypto.createDecipher(...)\n', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = 6d3eb22c4e8a0db496bdfaf8f1f1e5e93954625c7369acb8d3d89700a4bd63ce114e267a86abbcf7b08abc45e3bda8e0e9cd3918582a80ff8f36b301e337efc7_0
2025-06-02 17:44:54,836 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = 30ad92c285f157dfcf606c2e424ebbfceda68d2d91bfdf00791f0871a65bd55b4c7a47ccfd2888717d6c0aefc93ee200eb4c55ad7f2d3e12cad8034e9e890569_0
2025-06-02 17:44:54,836 - semgrep.rule_match - DEBUG - match_key = ('crypto.createCipher(...)\n crypto.createDecipher(...)\n', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = 6d3eb22c4e8a0db496bdfaf8f1f1e5e93954625c7369acb8d3d89700a4bd63ce114e267a86abbcf7b08abc45e3bda8e0e9cd3918582a80ff8f36b301e337efc7_0
2025-06-02 17:44:54,837 - semgrep.rule_match - DEBUG - match_key = ('crypto.createCipher(...)\n crypto.createDecipher(...)\n', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = 6d3eb22c4e8a0db496bdfaf8f1f1e5e93954625c7369acb8d3d89700a4bd63ce114e267a86abbcf7b08abc45e3bda8e0e9cd3918582a80ff8f36b301e337efc7_0
2025-06-02 17:44:54,838 - semgrep.rule_match - DEBUG - match_key = ('crypto.createCipher(...)\n crypto.createDecipher(...)\n', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = 6d3eb22c4e8a0db496bdfaf8f1f1e5e93954625c7369acb8d3d89700a4bd63ce114e267a86abbcf7b08abc45e3bda8e0e9cd3918582a80ff8f36b301e337efc7_1
2025-06-02 17:44:54,838 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes/quizExpressRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = b6693b4854b5d64dcb3bcfe5e30cb2fb695b7d6ecf596fa6e6103ff36149ec1b26b5460b75c59ea0b6cd01f12db750db79429cbe46096ffe1aa952ab90edb6ce_0
2025-06-02 17:44:54,839 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching quiz quizId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching quiz quizId:,error.message,...)\n console.error(`Error fetching quiz quizId:,error.message,...)\n', PosixPath('server/routes/quizExpressRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 6914aab007cdf4f4bca7a5d021c09ca365a12fa99ff89fb919da59ecc13a1e2060f07beb4b00e9c0f49e385d34e1078492a9270b7ab1332d8818e1ec38699629_0
2025-06-02 17:44:54,840 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching quiz quizId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching quiz quizId:,error.message,...)\n console.error(`Error fetching quiz quizId:,error.message,...)\n', PosixPath('server/routes/quizExpressRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 6914aab007cdf4f4bca7a5d021c09ca365a12fa99ff89fb919da59ecc13a1e2060f07beb4b00e9c0f49e385d34e1078492a9270b7ab1332d8818e1ec38699629_0
2025-06-02 17:44:54,840 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching quiz quizId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching quiz quizId:,error.message,...)\n console.error(`Error fetching quiz quizId:,error.message,...)\n', PosixPath('server/routes/quizExpressRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 6914aab007cdf4f4bca7a5d021c09ca365a12fa99ff89fb919da59ecc13a1e2060f07beb4b00e9c0f49e385d34e1078492a9270b7ab1332d8818e1ec38699629_0
2025-06-02 17:44:54,841 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = ebf71f340ac9c8c239c05872f11306f6dbee70123ef74ab019c5cc08204f85915d352ea58aa7dd5a7a1bf4c2cd57e3f091135f739d5dc48872548fc9ceb74ff6_0
2025-06-02 17:44:54,842 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `quizRoutes: Auth Error - supabase.auth.getUser returned an error: getUserError.message $UTIL = require(\'util\')\n...\n $UTIL.format(`quizRoutes: Auth Error - supabase.auth.getUser returned an error: getUserError.message,getUserError,...)\n console.error(`quizRoutes: Auth Error - supabase.auth.getUser returned an error: getUserError.message,getUserError,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 8544d25c24ce0f4cfa66f96a8f5bc59f8a700969b0373f9798d24c9b18c9d00aa4503eea8cdbd561f130dceace7eb6046e909d8a4f2477f5d61fe516ed9fa82b_0
2025-06-02 17:44:54,842 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `quizRoutes: Auth Error - supabase.auth.getUser returned an error: getUserError.message $UTIL = require(\'util\')\n...\n $UTIL.format(`quizRoutes: Auth Error - supabase.auth.getUser returned an error: getUserError.message,getUserError,...)\n console.error(`quizRoutes: Auth Error - supabase.auth.getUser returned an error: getUserError.message,getUserError,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 8544d25c24ce0f4cfa66f96a8f5bc59f8a700969b0373f9798d24c9b18c9d00aa4503eea8cdbd561f130dceace7eb6046e909d8a4f2477f5d61fe516ed9fa82b_0
2025-06-02 17:44:54,843 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `quizRoutes: Auth Error - supabase.auth.getUser returned an error: getUserError.message $UTIL = require(\'util\')\n...\n $UTIL.format(`quizRoutes: Auth Error - supabase.auth.getUser returned an error: getUserError.message,getUserError,...)\n console.error(`quizRoutes: Auth Error - supabase.auth.getUser returned an error: getUserError.message,getUserError,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 8544d25c24ce0f4cfa66f96a8f5bc59f8a700969b0373f9798d24c9b18c9d00aa4503eea8cdbd561f130dceace7eb6046e909d8a4f2477f5d61fe516ed9fa82b_0
2025-06-02 17:44:54,844 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = ebf71f340ac9c8c239c05872f11306f6dbee70123ef74ab019c5cc08204f85915d352ea58aa7dd5a7a1bf4c2cd57e3f091135f739d5dc48872548fc9ceb74ff6_0
2025-06-02 17:44:54,844 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error downloading content for document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error downloading content for document docId:,downloadError,...)\n console.error(`Error downloading content for document docId:,downloadError,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e0a949512836e4ed99d6e8aefe9ad2f587f5413311e9573a2c016aae76021a1cb1865e1fd6d57dedfc666923be4049f8124b5cbfb81b9cb81217d32efcf9d6cc_0
2025-06-02 17:44:54,845 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error downloading content for document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error downloading content for document docId:,downloadError,...)\n console.error(`Error downloading content for document docId:,downloadError,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e0a949512836e4ed99d6e8aefe9ad2f587f5413311e9573a2c016aae76021a1cb1865e1fd6d57dedfc666923be4049f8124b5cbfb81b9cb81217d32efcf9d6cc_0
2025-06-02 17:44:54,846 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error downloading content for document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error downloading content for document docId:,downloadError,...)\n console.error(`Error downloading content for document docId:,downloadError,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e0a949512836e4ed99d6e8aefe9ad2f587f5413311e9573a2c016aae76021a1cb1865e1fd6d57dedfc666923be4049f8124b5cbfb81b9cb81217d32efcf9d6cc_0
2025-06-02 17:44:54,847 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = ebf71f340ac9c8c239c05872f11306f6dbee70123ef74ab019c5cc08204f85915d352ea58aa7dd5a7a1bf4c2cd57e3f091135f739d5dc48872548fc9ceb74ff6_0
2025-06-02 17:44:54,854 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n combinedTextContent + `Content from document.file_name:\\n docTextContent\\n\\n combinedTextContent.concat(`Content from document.file_name:\\n docTextContent\\n\\n) `...${...}...`\n combinedTextContent.concat("...")\n `Quiz Generation: AI generated aiGeneratedData.questions.length questions with types: $UTIL = require(\'util\')\n...\n $UTIL.format(`Quiz Generation: AI generated aiGeneratedData.questions.length questions with types:,typeCounts,...)\n console.log(`Quiz Generation: AI generated aiGeneratedData.questions.length questions with types:,typeCounts,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 792a2aeefd84f6db4ea621bf3d5a520b51c992b2d89512053ce92d7591d2c71223643a17430ba01900104f9f995eda19afef74c0b44f110dbea5f200da4e81bb_0
2025-06-02 17:44:54,855 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n combinedTextContent + `Content from document.file_name:\\n docTextContent\\n\\n combinedTextContent.concat(`Content from document.file_name:\\n docTextContent\\n\\n) `...${...}...`\n combinedTextContent.concat("...")\n `Quiz Generation: AI generated aiGeneratedData.questions.length questions with types: $UTIL = require(\'util\')\n...\n $UTIL.format(`Quiz Generation: AI generated aiGeneratedData.questions.length questions with types:,typeCounts,...)\n console.log(`Quiz Generation: AI generated aiGeneratedData.questions.length questions with types:,typeCounts,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 792a2aeefd84f6db4ea621bf3d5a520b51c992b2d89512053ce92d7591d2c71223643a17430ba01900104f9f995eda19afef74c0b44f110dbea5f200da4e81bb_0
2025-06-02 17:44:54,856 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n combinedTextContent + `Content from document.file_name:\\n docTextContent\\n\\n combinedTextContent.concat(`Content from document.file_name:\\n docTextContent\\n\\n) `...${...}...`\n combinedTextContent.concat("...")\n `Quiz Generation: AI generated aiGeneratedData.questions.length questions with types: $UTIL = require(\'util\')\n...\n $UTIL.format(`Quiz Generation: AI generated aiGeneratedData.questions.length questions with types:,typeCounts,...)\n console.log(`Quiz Generation: AI generated aiGeneratedData.questions.length questions with types:,typeCounts,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 792a2aeefd84f6db4ea621bf3d5a520b51c992b2d89512053ce92d7591d2c71223643a17430ba01900104f9f995eda19afef74c0b44f110dbea5f200da4e81bb_0
2025-06-02 17:44:54,857 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = ebf71f340ac9c8c239c05872f11306f6dbee70123ef74ab019c5cc08204f85915d352ea58aa7dd5a7a1bf4c2cd57e3f091135f739d5dc48872548fc9ceb74ff6_0
2025-06-02 17:44:54,858 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error checking for document firstDocId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error checking for document firstDocId:,docCheckError.message,...)\n console.error(`Error checking for document firstDocId:,docCheckError.message,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = c7558bfde50b68740e29614fb2fcbb2e537650de3c69da509d637a3294b120666f3eab865db05db7d2cb797b42b66fc6b5008123cfb23209ccf2c97ebb24724a_0
2025-06-02 17:44:54,859 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error checking for document firstDocId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error checking for document firstDocId:,docCheckError.message,...)\n console.error(`Error checking for document firstDocId:,docCheckError.message,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = c7558bfde50b68740e29614fb2fcbb2e537650de3c69da509d637a3294b120666f3eab865db05db7d2cb797b42b66fc6b5008123cfb23209ccf2c97ebb24724a_0
2025-06-02 17:44:54,861 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error checking for document firstDocId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error checking for document firstDocId:,docCheckError.message,...)\n console.error(`Error checking for document firstDocId:,docCheckError.message,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = c7558bfde50b68740e29614fb2fcbb2e537650de3c69da509d637a3294b120666f3eab865db05db7d2cb797b42b66fc6b5008123cfb23209ccf2c97ebb24724a_0
2025-06-02 17:44:54,861 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e3ac40a0e0e61cabaa8944aa579643082a4a77fb689f81c0fd9d7aa649523a226adcb6751dd032bdffdc157e19e62ca893744bd6f03e341d40f28d35afaa6fb5_0
2025-06-02 17:44:54,863 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Generating quiz \'quizName\' for document documentId, user userId, options: $UTIL = require(\'util\')\n...\n $UTIL.format(`Generating quiz \'quizName\' for document documentId, user userId, options:,generationOptions,...)\n console.log(`Generating quiz \'quizName\' for document documentId, user userId, options:,generationOptions,...)\n', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 18e6ba934145c2d46fe4bca682c0df5192cae44838e34e2163571c014925daaa25c5ccbab490b845e2b8968888e66b49908031d4433ceef56b443fb0f8fe298d_0
2025-06-02 17:44:54,864 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Generating quiz \'quizName\' for document documentId, user userId, options: $UTIL = require(\'util\')\n...\n $UTIL.format(`Generating quiz \'quizName\' for document documentId, user userId, options:,generationOptions,...)\n console.log(`Generating quiz \'quizName\' for document documentId, user userId, options:,generationOptions,...)\n', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 18e6ba934145c2d46fe4bca682c0df5192cae44838e34e2163571c014925daaa25c5ccbab490b845e2b8968888e66b49908031d4433ceef56b443fb0f8fe298d_0
2025-06-02 17:44:54,865 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Generating quiz \'quizName\' for document documentId, user userId, options: $UTIL = require(\'util\')\n...\n $UTIL.format(`Generating quiz \'quizName\' for document documentId, user userId, options:,generationOptions,...)\n console.log(`Generating quiz \'quizName\' for document documentId, user userId, options:,generationOptions,...)\n', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 18e6ba934145c2d46fe4bca682c0df5192cae44838e34e2163571c014925daaa25c5ccbab490b845e2b8968888e66b49908031d4433ceef56b443fb0f8fe298d_0
2025-06-02 17:44:54,867 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e3ac40a0e0e61cabaa8944aa579643082a4a77fb689f81c0fd9d7aa649523a226adcb6751dd032bdffdc157e19e62ca893744bd6f03e341d40f28d35afaa6fb5_0
2025-06-02 17:44:54,867 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `OpenRouter API error: aiResponse.status aiResponse.statusText $UTIL = require(\'util\')\n...\n $UTIL.format(`OpenRouter API error: aiResponse.status aiResponse.statusText,errorBody,...)\n console.error(`OpenRouter API error: aiResponse.status aiResponse.statusText,errorBody,...)\n', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = d85783d38cf827356591f5c95a04cfd3aeefd441a0ce032041e62f04f45fdbf92ba05c11ad1bcdef4516986f7840bebcecc4cf10f7bd5d31e7159e2b904cec26_0
2025-06-02 17:44:54,868 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `OpenRouter API error: aiResponse.status aiResponse.statusText $UTIL = require(\'util\')\n...\n $UTIL.format(`OpenRouter API error: aiResponse.status aiResponse.statusText,errorBody,...)\n console.error(`OpenRouter API error: aiResponse.status aiResponse.statusText,errorBody,...)\n', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = d85783d38cf827356591f5c95a04cfd3aeefd441a0ce032041e62f04f45fdbf92ba05c11ad1bcdef4516986f7840bebcecc4cf10f7bd5d31e7159e2b904cec26_0
2025-06-02 17:44:54,869 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `OpenRouter API error: aiResponse.status aiResponse.statusText $UTIL = require(\'util\')\n...\n $UTIL.format(`OpenRouter API error: aiResponse.status aiResponse.statusText,errorBody,...)\n console.error(`OpenRouter API error: aiResponse.status aiResponse.statusText,errorBody,...)\n', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = d85783d38cf827356591f5c95a04cfd3aeefd441a0ce032041e62f04f45fdbf92ba05c11ad1bcdef4516986f7840bebcecc4cf10f7bd5d31e7159e2b904cec26_0
2025-06-02 17:44:54,870 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 38dff96e071f012b2148df3dd79bcad59133ff3ed6bbd12708735ef01894846a70b99f4a43c7d3f967a9b3396331fc2e3e230b78a5299aa0f73cb4e3b78d113d_0
2025-06-02 17:44:54,871 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 68c17c0ea96ceead0340afb10c60c4539f96f640d185bad2b8435aa651982d1091f124d48a2807a2d557a8ac3f1b9b378cf9d703c35d98a5801c3e7814b3239d_0
2025-06-02 17:44:54,872 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 68c17c0ea96ceead0340afb10c60c4539f96f640d185bad2b8435aa651982d1091f124d48a2807a2d557a8ac3f1b9b378cf9d703c35d98a5801c3e7814b3239d_0
2025-06-02 17:44:54,873 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 68c17c0ea96ceead0340afb10c60c4539f96f640d185bad2b8435aa651982d1091f124d48a2807a2d557a8ac3f1b9b378cf9d703c35d98a5801c3e7814b3239d_0
2025-06-02 17:44:54,874 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = de5146047ed4bf20c298ee7cc94c74eecab963f091b90d6117b34411fdda2600e1f5cf8447aac4fb26e31bf1e783ab9e970af3fc88172c367fcfd84fd61b4f5a_0
2025-06-02 17:44:54,875 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 632b3c9f657d5f9e419e45a65e73cb8e26aa1c9a5290120733812a420bb2ec3aad29d5d0bec6bce47485aa0f086afc3a7ead85da88f90c2fe86b2b502659b003_0
2025-06-02 17:44:54,876 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 632b3c9f657d5f9e419e45a65e73cb8e26aa1c9a5290120733812a420bb2ec3aad29d5d0bec6bce47485aa0f086afc3a7ead85da88f90c2fe86b2b502659b003_0
2025-06-02 17:44:54,877 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 632b3c9f657d5f9e419e45a65e73cb8e26aa1c9a5290120733812a420bb2ec3aad29d5d0bec6bce47485aa0f086afc3a7ead85da88f90c2fe86b2b502659b003_0
2025-06-02 17:44:54,878 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = f30d1e90cbded1cbd6c553454cd00b085f2cfd03abf7e2ad6a1ddaf71e4ae17bd5f530c7e0973773cf33570acf3504a75b84dc078b8f9e51ff6e68e7c8d513f5_0
2025-06-02 17:44:54,878 - semgrep.rule_match - DEBUG - match_key = ('crypto.createCipher(...)\n crypto.createDecipher(...)\n', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = ad2e8e87a452b9039a795d6557abe6bdd336fb900f230489e32ac83eea571bed45d32975a103cbd01f0d9c6c9d2fef4b65c519d133ed7319a3e0768a26b1967e_0
2025-06-02 17:44:54,879 - semgrep.rule_match - DEBUG - match_key = ('crypto.createCipher(...)\n crypto.createDecipher(...)\n', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = ad2e8e87a452b9039a795d6557abe6bdd336fb900f230489e32ac83eea571bed45d32975a103cbd01f0d9c6c9d2fef4b65c519d133ed7319a3e0768a26b1967e_0
2025-06-02 17:44:54,881 - semgrep.rule_match - DEBUG - match_key = ('crypto.createCipher(...)\n crypto.createDecipher(...)\n', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = ad2e8e87a452b9039a795d6557abe6bdd336fb900f230489e32ac83eea571bed45d32975a103cbd01f0d9c6c9d2fef4b65c519d133ed7319a3e0768a26b1967e_0
2025-06-02 17:44:54,882 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = f30d1e90cbded1cbd6c553454cd00b085f2cfd03abf7e2ad6a1ddaf71e4ae17bd5f530c7e0973773cf33570acf3504a75b84dc078b8f9e51ff6e68e7c8d513f5_0
2025-06-02 17:44:54,883 - semgrep.rule_match - DEBUG - match_key = ('crypto.createCipher(...)\n crypto.createDecipher(...)\n', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = ad2e8e87a452b9039a795d6557abe6bdd336fb900f230489e32ac83eea571bed45d32975a103cbd01f0d9c6c9d2fef4b65c519d133ed7319a3e0768a26b1967e_0
2025-06-02 17:44:54,884 - semgrep.rule_match - DEBUG - match_key = ('crypto.createCipher(...)\n crypto.createDecipher(...)\n', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = ad2e8e87a452b9039a795d6557abe6bdd336fb900f230489e32ac83eea571bed45d32975a103cbd01f0d9c6c9d2fef4b65c519d133ed7319a3e0768a26b1967e_0
2025-06-02 17:44:54,884 - semgrep.rule_match - DEBUG - match_key = ('crypto.createCipher(...)\n crypto.createDecipher(...)\n', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.javascript.node-crypto.security.create-de-cipher-no-iv') match_id = ad2e8e87a452b9039a795d6557abe6bdd336fb900f230489e32ac83eea571bed45d32975a103cbd01f0d9c6c9d2fef4b65c519d133ed7319a3e0768a26b1967e_1
2025-06-02 17:44:54,885 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = 02b74353025fd37166a7276dc9eec13e229cb3caae5b7a3e51ec191a430f138bc68fc5aa03b2f45c430a2197db199dc14e7fb7a9ab785b0ab92a353dab6a367b_0
2025-06-02 17:44:54,885 - semgrep.rule_match - DEBUG - match_key = ('(?i)\\b(sk-[a-zA-Z0-9_-]+)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = 57c16f7eefd181af0aaedc34c0bd36524cc4c527dbd810184f26d99981547848d8b6f6333a03d3be666f8d656418f2a068e5d6ae98b7f8ed3778a0240fb5f272_0
2025-06-02 17:44:54,886 - semgrep.rule_match - DEBUG - match_key = ('(?i)\\b(sk-[a-zA-Z0-9_-]+)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = 57c16f7eefd181af0aaedc34c0bd36524cc4c527dbd810184f26d99981547848d8b6f6333a03d3be666f8d656418f2a068e5d6ae98b7f8ed3778a0240fb5f272_0
2025-06-02 17:44:54,887 - semgrep.rule_match - DEBUG - match_key = ('(?i)\\b(sk-[a-zA-Z0-9_-]+)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = 57c16f7eefd181af0aaedc34c0bd36524cc4c527dbd810184f26d99981547848d8b6f6333a03d3be666f8d656418f2a068e5d6ae98b7f8ed3778a0240fb5f272_0
2025-06-02 17:44:54,887 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = 02b74353025fd37166a7276dc9eec13e229cb3caae5b7a3e51ec191a430f138bc68fc5aa03b2f45c430a2197db199dc14e7fb7a9ab785b0ab92a353dab6a367b_0
2025-06-02 17:44:54,888 - semgrep.rule_match - DEBUG - match_key = ('(?i)\\b(sk-[a-zA-Z0-9_-]+)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = 57c16f7eefd181af0aaedc34c0bd36524cc4c527dbd810184f26d99981547848d8b6f6333a03d3be666f8d656418f2a068e5d6ae98b7f8ed3778a0240fb5f272_0
2025-06-02 17:44:54,889 - semgrep.rule_match - DEBUG - match_key = ('(?i)\\b(sk-[a-zA-Z0-9_-]+)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = 57c16f7eefd181af0aaedc34c0bd36524cc4c527dbd810184f26d99981547848d8b6f6333a03d3be666f8d656418f2a068e5d6ae98b7f8ed3778a0240fb5f272_0
2025-06-02 17:44:54,889 - semgrep.rule_match - DEBUG - match_key = ('(?i)\\b(sk-[a-zA-Z0-9_-]+)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('test-credentials.js'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = 57c16f7eefd181af0aaedc34c0bd36524cc4c527dbd810184f26d99981547848d8b6f6333a03d3be666f8d656418f2a068e5d6ae98b7f8ed3778a0240fb5f272_1
2025-06-02 17:44:54,893 - semgrep.core_runner - DEBUG - semgrep ran in 0:00:23.074866 on 208 files
2025-06-02 17:44:54,895 - semgrep.core_runner - DEBUG - findings summary: 4 warning, 14 error, 25 info
2025-06-02 17:44:54,900 - semgrep.app.auth - DEBUG - Getting API token from settings file
2025-06-02 17:44:54,901 - semgrep.app.auth - DEBUG - No API token found in settings file
2025-06-02 17:44:54,904 - semgrep.semgrep_core - DEBUG - Failed to open resource semgrep-core-proprietary: [Errno 2] No such file or directory: '/tmp/_MEIC0TmjD/semgrep/bin/semgrep-core-proprietary'.
2025-06-02 17:44:55,110 - semgrep.output - VERBOSE - 
========================================
Files skipped:
========================================

  Always skipped by Opengrep:

   • <none>

  Skipped by .gitignore:
  (Disable by passing --no-git-ignore)

   • <all files not listed by `git ls-files` were skipped>

  Skipped by .semgrepignore:
  - https://semgrep.dev/docs/ignoring-files-folders-code/#understand-semgrep-defaults

   • <none>

  Skipped by --include patterns:

   • <none>

  Skipped by --exclude patterns:

   • <none>

  Files skipped due to insufficient read permissions:

   • <none>

  Skipped by limiting to files smaller than 1000000 bytes:
  (Adjust with the --max-target-bytes flag)

   • generated-icon.png

  Partially analyzed due to parsing or internal Opengrep errors

   • tailwind.config.ts (1 lines skipped)

2025-06-02 17:44:55,111 - semgrep.output - INFO - Some files were skipped or only partially analyzed.
  Scan was limited to files tracked by git.
  Partially scanned: 1 files only partially analyzed due to parsing or internal Opengrep errors
  Scan skipped: 1 files larger than 1.0 MB
  For a full list of skipped files, run opengrep with the --verbose flag.

Ran 445 rules on 208 files: 43 findings.
2025-06-02 17:44:55,112 - semgrep.app.version - DEBUG - Version cache does not exist
2025-06-02 17:44:55,112 - semgrep.app.version - DEBUG - Version cache does not exist
2025-06-02 17:44:55,132 - semgrep.metrics - VERBOSE - Not sending pseudonymous metrics since metrics are configured to OFF and registry usage is False
