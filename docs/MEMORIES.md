# ChewyAI System Memories

## 🧠 Important Architectural Decisions

### Security Architecture (2025-06-02)
**Decision**: Implement encrypted database storage for user AI provider credentials
**Rationale**:
- Users maintain control over their AI usage and costs
- No liability for ChewyAI regarding AI provider billing
- Enhanced security with AES-256-GCM encryption
- Compliance with data protection principles
- Better user experience (no need to re-enter credentials)

**Implementation**:
- User AI credentials encrypted with AES-256-GCM before database storage
- Row Level Security (RLS) policies enforce user data isolation
- API keys retrieved and decrypted only during AI API calls (ephemeral use)
- No logging of sensitive credentials or headers
- Client-side localStorage only stores non-sensitive configuration

**Security Audit Results (2025-06-02)**:
- Fixed critical hardcoded credentials vulnerability
- Eliminated client-side API key exposure risks
- Implemented secure encrypted credential storage
- Enhanced authentication and error handling

### Full-Stack Architecture (2025-06-02)
**Decision**: Single-server deployment with Express.js serving both API and static files
**Rationale**:
- Simplified deployment on Replit
- Reduced complexity compared to separate frontend/backend deployments
- Better performance with single-origin requests
- Easier CORS management

**Implementation**:
- Vite builds React app to `dist/public/`
- Express serves static files with proper caching
- API routes prefixed with `/api` to avoid conflicts
- SPA fallback routing for client-side navigation

### Database Strategy (2025-06-02)
**Decision**: Use Supabase as primary database with Row Level Security
**Rationale**:
- Built-in authentication and authorization
- Real-time capabilities for future features
- Managed PostgreSQL with good performance
- Row Level Security for data isolation

**Implementation**:
- Supabase client for frontend authentication
- Service role key for backend operations
- RLS policies enforce user data access
- JWT tokens for API authentication

### Build System (2025-06-02)
**Decision**: Vite for frontend, esbuild for backend bundling
**Rationale**:
- Fast development builds with Vite
- Optimized production builds with code splitting
- Single JavaScript bundle for easy deployment
- TypeScript support throughout

**Implementation**:
- Vite handles React app with optimizations
- esbuild bundles server code with minification
- Separate build steps for frontend and backend
- Production-ready asset optimization

## 🔧 Technical Implementation Details

### API Route Organization
**Current Structure**:
- `/api/health` - Health checks and monitoring
- `/api/flashcards/*` - Flashcard CRUD operations
- `/api/quizzes/*` - Quiz management and generation
- `/api/documents/*` - Document processing and storage
- `/api/ai/*` - AI integration endpoints

**Design Principles**:
- RESTful API design where applicable
- Consistent error response format
- Proper HTTP status codes
- Input validation with Zod schemas

### Environment Variable Strategy
**Development vs Production**:
- Development: Fallback values for quick setup
- Production: Required environment variables with validation
- Client variables prefixed with `VITE_`
- Server variables without prefix

**Security Considerations**:
- No sensitive fallbacks in production
- Startup validation for required variables
- Clear separation of client/server configuration

### Static File Serving Strategy
**Caching Strategy**:
- HTML files: No cache (always fresh)
- JS/CSS asse ts: 1 year cache with immutable flag
- Images and fonts: Standard caching
- API responses: No cache by default

**Security Headers**:
- Content Security Policy for XSS protection
- Frame options to prevent clickjacking
- Content type sniffing prevention
- Referrer policy for privacy

## 🚀 Deployment Decisions

### Replit Configuration
**Deployment Target**: Autoscale
**Rationale**: 
- Automatic scaling based on traffic
- Cost-effective for variable usage
- Built-in load balancing
- Easy environment management

**Build Process**:
- Single build command: `npm run build`
- Health check endpoint: `/api/health`
- Environment variables in deployment config
- Automatic HTTPS and domain management

### Production Optimizations
**Frontend Optimizations**:
- Code splitting by vendor and route
- Asset hashing for cache busting
- Tree shaking for smaller bundles
- Lazy loading for non-critical components

**Backend Optimizations**:
- Minified server bundle
- External package handling
- Efficient static file serving
- Proper error handling without stack traces

## 📝 Development Workflow Decisions

### Package Management
**Decision**: Use npm as primary package manager
**Rationale**:
- Consistent with Node.js ecosystem
- Good lock file support
- Wide compatibility
- Replit native support

### TypeScript Configuration
**Decision**: Strict TypeScript throughout the application
**Rationale**:
- Better developer experience
- Catch errors at compile time
- Improved code documentation
- Better IDE support

### Testing Strategy
**Current Approach**: Build verification and manual testing
**Future Considerations**:
- Unit tests for critical business logic
- Integration tests for API endpoints
- End-to-end tests for user workflows
- Performance testing for production loads

## 🔄 Evolution and Future Considerations

### Scalability Considerations
- Database connection pooling for high traffic
- CDN integration for static assets
- Caching layer for frequently accessed data
- Microservices migration if needed

### Security Enhancements
- Rate limiting for API endpoints
- Advanced monitoring and alerting
- Automated security scanning
- Regular dependency updates

### Feature Expansion
- Real-time collaboration features
- Advanced AI model support
- Mobile application development
- Offline functionality

## 📊 Performance Benchmarks

### Current Performance Targets
- Page load time: < 2 seconds
- API response time: < 500ms
- Build time: < 2 minutes
- Bundle size: < 1MB (gzipped)

### Monitoring Metrics
- Health check response time
- Error rates by endpoint
- User authentication success rate
- AI provider integration reliability
